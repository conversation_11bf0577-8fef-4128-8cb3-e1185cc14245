import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import Anthropic from '@anthropic-ai/sdk';
import { AnthropicModelDetail, ModelInfo } from './interfaces/anthropic.interface';

/**
 * Service for interacting with Anthropic API
 */
@Injectable()
export class AnthropicService {
  private readonly logger = new Logger(AnthropicService.name);

  constructor() {}

  /**
   * L<PERSON>y danh sách model từ Anthropic
   * @param apiKey API key của Anthropic
   * @param limit Giới hạn số lượng model trả về
   * @returns Danh sách model từ Anthropic
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async getModels(apiKey: string, limit?: number): Promise<ModelInfo[]> {
    try {
      if (!apiKey) {
        throw new Error('API key is required to list models');
      }

      const client = this.createClient(apiKey);

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi API để lấy danh sách model
      const response = await client.models.list({
        signal: controller.signal,
        query: limit ? limit : undefined,
      });

      clearTimeout(timeoutId);

      return response.data;
    } catch (error: any) {
      this.handleApiError(error, 'lấy danh sách model');
    }
  }

  /**
   * Lấy thông tin chi tiết của một model theo ID
   * @param modelId ID của model cần lấy thông tin (ví dụ: claude-3-opus-20240229)
   * @param apiKey API key của Anthropic
   * @returns Thông tin chi tiết của model
   * @throws AppException nếu có lỗi khi lấy thông tin model
   */
  async retrieveModel(modelId: string, apiKey: string): Promise<AnthropicModelDetail> {
    try {
      // Kiểm tra modelId
      if (!modelId) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Model ID không được để trống',
        );
      }

      // Kiểm tra apiKey
      if (!apiKey) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'API key không được để trống',
        );
      }

      const client = this.createClient(apiKey);

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi API để lấy thông tin chi tiết của model
      const response = await client.models.retrieve(modelId, {
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Kiểm tra kết quả
      if (!response || !response.id) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy model với ID: ${modelId}`,
        );
      }

      return response;
    } catch (error: any) {
      // Xử lý trường hợp không tìm thấy model (404)
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy model với ID: ${modelId}`,
        );
      }

      this.handleApiError(error, `lấy thông tin model ${modelId}`);
    }
  }

  /**
   * Xử lý lỗi từ API
   * @param error Lỗi từ API
   * @param action Hành động đang thực hiện
   * @throws AppException với mã lỗi và thông báo phù hợp
   */
  private handleApiError(error: any, action: string): never {
    this.logger.error(
      `Error ${action} from Anthropic: ${error.message}`,
      error.stack,
    );

    // Xử lý các lỗi khi kết nối Anthropic API
    if (error.status === 429) {
      throw new AppException(
        ErrorCode.OPENAI_QUOTA_EXCEEDED,
        'Đã vượt quá giới hạn sử dụng Anthropic API',
      );
    }

    if (error.status === 401 || error.status === 403) {
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi xác thực API key: ' + (error.message || 'Không có quyền truy cập'),
      );
    }

    if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
      throw new AppException(
        ErrorCode.OPENAI_TIMEOUT,
        'Kết nối đến Anthropic API bị gián đoạn hoặc quá thời gian chờ',
      );
    }

    if (error.name === 'NetworkError' || error.message.includes('network')) {
      throw new AppException(
        ErrorCode.OPENAI_TIMEOUT,
        'Lỗi kết nối đến Anthropic API',
      );
    }

    // Các lỗi khác
    throw new AppException(
      ErrorCode.OPENAI_API_ERROR,
      `Lỗi khi ${action}: ${error.message}`,
    );
  }

  /**
   * Tạo một instance Anthropic mới với API key tùy chỉnh
   * @param apiKey API key tùy chỉnh
   * @returns Instance Anthropic mới
   */
  createClient(apiKey: string): Anthropic {
    if (!apiKey) {
      this.logger.warn('Empty API key provided to createClient');
    }

    return new Anthropic({
      apiKey: apiKey || '',
    });
  }
}
