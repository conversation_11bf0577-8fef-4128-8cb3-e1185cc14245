import { Injectable } from '@nestjs/common';
import { ImageGenerationService } from '../interfaces';
import { OpenAiDalleService } from './openai-dalle.service';
import { StabilityAiService } from './stability-ai.service';
import { LeonardoAiService } from './leonardo-ai.service';

/**
 * Enum for image generation provider types
 */
export enum ImageGenerationProviderType {
  OPENAI_DALLE = 'OPENAI_DALLE',
  STABILITY_AI = 'STABILITY_AI',
  LEONARDO_AI = 'LEONARDO_AI',
}

/**
 * Factory service for creating image generation services
 */
@Injectable()
export class ImageGenerationFactoryService {
  constructor(
    private readonly openAiDalleService: OpenAiDalleService,
    private readonly stabilityAiService: StabilityAiService,
    private readonly leonardoAiService: LeonardoAiService,
  ) {}

  /**
   * Get an image generation service by provider type
   * @param providerType Provider type
   * @returns Image generation service
   * @throws Error if provider type is not supported
   */
  getProvider(providerType: ImageGenerationProviderType): ImageGenerationService {
    switch (providerType) {
      case ImageGenerationProviderType.OPENAI_DALLE:
        return this.openAiDalleService;
      case ImageGenerationProviderType.STABILITY_AI:
        return this.stabilityAiService;
      case ImageGenerationProviderType.LEONARDO_AI:
        return this.leonardoAiService;
      default:
        throw new Error(`Unsupported image generation provider type: ${providerType}`);
    }
  }

  /**
   * Get all available image generation services
   * @returns Array of image generation services
   */
  getAllProviders(): ImageGenerationService[] {
    return [
      this.openAiDalleService,
      this.stabilityAiService,
      this.leonardoAiService,
    ];
  }

  /**
   * Get all available provider types
   * @returns Array of provider types
   */
  getAllProviderTypes(): ImageGenerationProviderType[] {
    return [
      ImageGenerationProviderType.OPENAI_DALLE,
      ImageGenerationProviderType.STABILITY_AI,
      ImageGenerationProviderType.LEONARDO_AI,
    ];
  }
}
