/**
 * Enum định nghĩa các loại media
 */
export enum MediaType {
  IMAGE_JPEG = 'image/jpeg',
  IMAGE_PNG = 'image/png',
  IMAGE_GIF = 'image/gif',
  APPLICATION_PDF = 'application/pdf',
  TEXT_PLAIN = 'text/plain',
  APPLICATION_JSON = 'application/json',
  APPLICATION_OCTET_STREAM = 'application/octet-stream',
}

/**
 * Utility class để làm việc với MediaType
 */
export class MediaTypeUtil {
  /**
   * Lấy giá trị MediaType từ string
   * @param value Giá trị string
   * @returns MediaType tương ứng hoặc APPLICATION_OCTET_STREAM nếu không tìm thấy
   */
  static getValue(value: string): MediaType {
    const mediaType = Object.values(MediaType).find(type => type === value);
    return mediaType || MediaType.APPLICATION_OCTET_STREAM;
  }

  /**
   * Kiểm tra xem một giá trị có phải là MediaType hợp lệ không
   * @param value Giá trị cần kiểm tra
   * @returns true nếu là MediaType hợp lệ, false nếu không
   */
  static isValid(value: string): boolean {
    return Object.values(MediaType).includes(value as MediaType);
  }

  /**
   * Lấy extension từ MediaType
   * @param mediaType MediaType
   * @returns Extension tương ứng (không bao gồm dấu chấm)
   */
  static getExtension(mediaType: MediaType): string {
    switch (mediaType) {
      case MediaType.IMAGE_JPEG:
        return 'jpg';
      case MediaType.IMAGE_PNG:
        return 'png';
      case MediaType.IMAGE_GIF:
        return 'gif';
      case MediaType.APPLICATION_PDF:
        return 'pdf';
      case MediaType.TEXT_PLAIN:
        return 'txt';
      case MediaType.APPLICATION_JSON:
        return 'json';
      default:
        return 'bin';
    }
  }
}
