import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { TypeAgent } from '../../agent/entities/type-agent.entity';
import { TypeAgentStatus } from '../../agent/constants';
import { PaginatedResult } from '../../../common/response/api-response-dto';

@Injectable()
export class TypeAgentRepository extends Repository<TypeAgent> {
  private readonly logger = new Logger(TypeAgentRepository.name);

  constructor(private dataSource: DataSource) {
    super(TypeAgent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho type agent
   * @returns SelectQueryBuilder<TypeAgent> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<TypeAgent> {
    return this.createQueryBuilder('typeAgent');
  }

  /**
   * Tìm loại agent theo ID
   * @param id ID của loại agent cần tìm
   * @returns Loại agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findTypeAgentById(id: number): Promise<TypeAgent | null> {
    return this.createBaseQuery()
      .where('typeAgent.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm loại agent theo tên
   * @param name Tên của loại agent cần tìm
   * @returns Loại agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findTypeAgentByName(name: string): Promise<TypeAgent | null> {
    return this.createBaseQuery()
      .where('typeAgent.name = :name', { name })
      .getOne();
  }

  /**
   * Lấy danh sách loại agent với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái loại agent (tùy chọn)
   * @param userId ID của người dùng (tùy chọn, nếu cần lọc theo người dùng)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách loại agent với phân trang
   */
  async findTypeAgents(
    page: number,
    limit: number,
    search?: string,
    status?: TypeAgentStatus,
    userId?: number,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<TypeAgent>> {
    const qb = this.createBaseQuery();

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(typeAgent.name ILIKE :search OR typeAgent.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('typeAgent.status = :status', { status });
    }

    // Thêm điều kiện lọc theo người dùng nếu có
    if (userId) {
      qb.andWhere('typeAgent.userId = :userId', { userId });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`typeAgent.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Lấy danh sách loại agent được phê duyệt
   * @returns Danh sách loại agent được phê duyệt
   */
  async findApprovedTypeAgents(): Promise<TypeAgent[]> {
    return this.createBaseQuery()
      .where('typeAgent.status = :status', { status: TypeAgentStatus.APPROVED })
      .getMany();
  }
}
