import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty } from 'class-validator';

/**
 * DTO cho việc bật/tắt trạng thái active của tool
 */
export class ToggleToolActiveDto {
  /**
   * Trạng thái active mới của tool
   */
  @ApiProperty({
    description: 'Trạng thái active mới của tool',
    example: true,
    required: true,
  })
  @IsNotEmpty({ message: 'Trạng thái active không được để trống' })
  @IsBoolean({ message: 'Trạng thái active phải là boolean' })
  active: boolean;
}
