import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

export const CUSTOM_TOOLS_ERROR_CODES = {
  // API Key errors
  API_KEY_NOT_FOUND: new ErrorCode(
    20350,
    'Không tìm thấy API Key',
    HttpStatus.NOT_FOUND,
  ),

  API_KEY_CREATE_FAILED: new ErrorCode(
    20351,
    'Tạo API Key thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  API_KEY_UPDATE_FAILED: new ErrorCode(
    20352,
    'Cập nhật API Key thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  API_KEY_DELETE_FAILED: new ErrorCode(
    20353,
    'Xóa API Key thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  API_KEY_INVALID: new ErrorCode(
    20354,
    'API Key không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // OAuth errors
  OAUTH_NOT_FOUND: new ErrorCode(
    20360,
    'Không tìm thấy cấu hình OAuth',
    HttpStatus.NOT_FOUND,
  ),

  OAUTH_CREATE_FAILED: new ErrorCode(
    20361,
    'Tạo cấu hình OAuth thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  OAUTH_UPDATE_FAILED: new ErrorCode(
    20362,
    'Cập nhật cấu hình OAuth thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  OAUTH_DELETE_FAILED: new ErrorCode(
    20363,
    'Xóa cấu hình OAuth thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  OAUTH_TOKEN_EXPIRED: new ErrorCode(
    20364,
    'Token OAuth đã hết hạn',
    HttpStatus.UNAUTHORIZED,
  ),

  OAUTH_REFRESH_FAILED: new ErrorCode(
    20365,
    'Làm mới token OAuth thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Custom Tool errors
  CUSTOM_TOOL_NOT_FOUND: new ErrorCode(
    20370,
    'Không tìm thấy công cụ tùy chỉnh',
    HttpStatus.NOT_FOUND,
  ),

  CUSTOM_TOOL_CREATE_FAILED: new ErrorCode(
    20371,
    'Tạo công cụ tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  CUSTOM_TOOL_UPDATE_FAILED: new ErrorCode(
    20372,
    'Cập nhật công cụ tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  CUSTOM_TOOL_DELETE_FAILED: new ErrorCode(
    20373,
    'Xóa công cụ tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  CUSTOM_TOOL_INVALID_PARAMETERS: new ErrorCode(
    20374,
    'Tham số công cụ tùy chỉnh không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  CUSTOM_TOOL_EXECUTION_FAILED: new ErrorCode(
    20375,
    'Thực thi công cụ tùy chỉnh thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  CUSTOM_TOOL_INVALID_ENDPOINT: new ErrorCode(
    20376,
    'Endpoint của công cụ tùy chỉnh không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  CUSTOM_TOOL_INVALID_METHOD: new ErrorCode(
    20377,
    'Phương thức HTTP của công cụ tùy chỉnh không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  CUSTOM_TOOL_INVALID_RESPONSE: new ErrorCode(
    20378,
    'Phản hồi từ công cụ tùy chỉnh không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Group Tool Mapping errors
  GROUP_TOOL_MAPPING_NOT_FOUND: new ErrorCode(
    20380,
    'Không tìm thấy ánh xạ nhóm công cụ',
    HttpStatus.NOT_FOUND,
  ),

  GROUP_TOOL_MAPPING_CREATE_FAILED: new ErrorCode(
    20381,
    'Tạo ánh xạ nhóm công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GROUP_TOOL_MAPPING_DELETE_FAILED: new ErrorCode(
    20382,
    'Xóa ánh xạ nhóm công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // OpenAPI errors
  OPENAPI_INVALID_FORMAT: new ErrorCode(
    20390,
    'Định dạng OpenAPI không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  OPENAPI_ENDPOINT_PROCESSING_FAILED: new ErrorCode(
    20391,
    'Xử lý endpoint OpenAPI thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  OPENAPI_SCHEMA_EXTRACTION_FAILED: new ErrorCode(
    20392,
    'Trích xuất schema từ OpenAPI thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
