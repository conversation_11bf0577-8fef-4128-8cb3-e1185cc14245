import { AppException } from '@common/exceptions';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { CreateAgentModularDto } from '../dto/agent/create-agent-modular.dto';

/**
 * Helper để validate dữ liệu agent theo c<PERSON>u hình TypeAgent
 */
export class AgentValidationHelper {
  /**
   * Validate dữ liệu agent theo c<PERSON><PERSON> hình TypeAgent
   * @param createDto Dữ liệu tạo agent
   * @param typeAgentConfig Cấu hình TypeAgent
   * @param userId ID của user hiện tại
   * @param vectorStoreRepository Repository để kiểm tra vector store
   * @throws AppException nếu validation fail
   */
  static async validateAgentDataByTypeConfig(
    createDto: CreateAgentModularDto,
    typeAgentConfig: TypeAgentConfig,
    userId: number,
    vectorStoreRepository: any,
  ): Promise<void> {
    // Validate Profile block
    this.validateProfileBlock(createDto, typeAgentConfig);

    // Validate Output block
    this.validateOutputBlock(createDto, typeAgentConfig);

    // Validate Resources block
    this.validateResourcesBlock(createDto, typeAgentConfig);

    // Validate Strategy block
    this.validateStrategyBlock(createDto, typeAgentConfig);

    // Validate Multi Agent block
    this.validateMultiAgentBlock(createDto, typeAgentConfig);

    // Validate Conversion block (nếu cần)
    this.validateConversionBlock(createDto, typeAgentConfig);

    // Validate Vector Store ownership
    await this.validateVectorStore(createDto.vectorStoreId, typeAgentConfig, userId, vectorStoreRepository);
  }

  /**
   * Validate Profile block - Validation lỏng hơn
   */
  private static validateProfileBlock(
    createDto: CreateAgentModularDto,
    typeAgentConfig: TypeAgentConfig,
  ): void {
    if (typeAgentConfig.hasProfile) {
      // Profile không bắt buộc, chỉ validate nếu được cung cấp
      if (createDto.profile) {
        // Validation lỏng hơn - chỉ cần có ít nhất một field
        // Không bắt buộc gender và dateOfBirth nữa
        console.log('Profile được cung cấp:', createDto.profile);
      }
    } else {
      // Nếu TypeAgent không hỗ trợ profile nhưng được cung cấp
      if (createDto.profile) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_PROFILE_NOT_SUPPORTED,
          'Loại agent này không hỗ trợ profile',
          { typeAgentConfig: typeAgentConfig.hasProfile }
        );
      }
    }
  }

  /**
   * Validate Output block - Validation lỏng hơn
   */
  private static validateOutputBlock(
    createDto: CreateAgentModularDto,
    typeAgentConfig: TypeAgentConfig,
  ): void {
    if (typeAgentConfig.hasOutput) {
      // Output không bắt buộc, có thể để trống
      if (createDto.output) {
        // Không bắt buộc phải có output channel, có thể để trống
        console.log('Output được cung cấp:', createDto.output);
      }
    } else {
      // Nếu TypeAgent không hỗ trợ output nhưng được cung cấp
      if (createDto.output) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Loại agent này không hỗ trợ output configuration',
          { typeAgentConfig: typeAgentConfig.hasOutput }
        );
      }
    }
  }

  /**
   * Validate Resources block - Validation lỏng hơn
   */
  private static validateResourcesBlock(
    createDto: CreateAgentModularDto,
    typeAgentConfig: TypeAgentConfig,
  ): void {
    if (typeAgentConfig.hasResources) {
      // Resources không bắt buộc, có thể để trống
      if (createDto.resources) {
        // Không bắt buộc phải có resource, có thể để trống
        console.log('Resources được cung cấp:', createDto.resources);
      }
    } else {
      // Nếu TypeAgent không hỗ trợ resources nhưng được cung cấp
      if (createDto.resources) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_RESOURCES_NOT_SUPPORTED,
          'Loại agent này không hỗ trợ resources',
          { typeAgentConfig: typeAgentConfig.hasResources }
        );
      }
    }
  }

  /**
   * Validate Strategy block - Validation lỏng hơn
   */
  private static validateStrategyBlock(
    createDto: CreateAgentModularDto,
    typeAgentConfig: TypeAgentConfig,
  ): void {
    if (typeAgentConfig.hasStrategy) {
      // Strategy không bắt buộc, có thể để trống
      if (createDto.strategy) {
        // Không bắt buộc phải có strategyId, có thể để trống
        console.log('Strategy được cung cấp:', createDto.strategy);
      }
    } else {
      // Nếu TypeAgent không hỗ trợ strategy nhưng được cung cấp
      if (createDto.strategy) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_STRATEGY_NOT_SUPPORTED,
          'Loại agent này không hỗ trợ strategy',
          { typeAgentConfig: typeAgentConfig.hasStrategy }
        );
      }
    }
  }

  /**
   * Validate Multi Agent block - Validation lỏng hơn
   */
  private static validateMultiAgentBlock(
    createDto: CreateAgentModularDto,
    typeAgentConfig: TypeAgentConfig,
  ): void {
    if (typeAgentConfig.hasMultiAgent) {
      // Multi Agent không bắt buộc, có thể để trống
      if (createDto.multiAgent) {
        // Không bắt buộc phải có agents, có thể để trống
        console.log('Multi Agent được cung cấp:', createDto.multiAgent);

        // Chỉ validate nếu có agents trong array
        if (createDto.multiAgent.multiAgent && createDto.multiAgent.multiAgent.length > 0) {
          // Validate từng item trong multiAgent array (chỉ khi có)
          for (const item of createDto.multiAgent.multiAgent) {
            if (!item.agent_id || !item.prompt) {
              throw new AppException(
                AGENT_ERROR_CODES.AGENT_MULTI_AGENT_INCOMPLETE,
                'Mỗi agent trong multi agent phải có agent_id và prompt',
                { invalidItem: item }
              );
            }
          }
        }
      }
    } else {
      // Nếu TypeAgent không hỗ trợ multi agent nhưng được cung cấp
      if (createDto.multiAgent) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_MULTI_AGENT_NOT_SUPPORTED,
          'Loại agent này không hỗ trợ multi agent',
          { typeAgentConfig: typeAgentConfig.hasMultiAgent }
        );
      }
    }
  }

  /**
   * Validate Conversion block
   */
  private static validateConversionBlock(
    createDto: CreateAgentModularDto,
    typeAgentConfig: TypeAgentConfig,
  ): void {
    if (typeAgentConfig.hasConversion) {
      // Conversion logic sẽ được implement sau
      // Hiện tại chỉ log để biết TypeAgent hỗ trợ conversion
      console.log('TypeAgent supports conversion:', typeAgentConfig.hasConversion);
    }
  }

  /**
   * Validate instruction theo TypeAgent config
   */
  static validateInstruction(
    instruction: string | undefined,
    typeAgentConfig: TypeAgentConfig,
  ): void {
    // Instruction luôn optional, không cần validate theo TypeAgent config
    // Chỉ validate format nếu được cung cấp
    if (instruction && instruction.trim().length === 0) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_INSTRUCTION_INVALID,
        'Instruction không được để trống nếu được cung cấp',
        { instruction }
      );
    }
  }

  /**
   * Validate vector store theo TypeAgent config và ownership
   */
  static async validateVectorStore(
    vectorStoreId: string | undefined,
    typeAgentConfig: TypeAgentConfig,
    userId: number,
    vectorStoreRepository: any,
  ): Promise<void> {
    // Vector store luôn optional, không cần validate theo TypeAgent config
    // Chỉ validate format và ownership nếu được cung cấp
    if (vectorStoreId) {
      if (vectorStoreId.trim().length === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_VECTOR_STORE_INVALID,
          'Vector store ID không được để trống nếu được cung cấp',
          { vectorStoreId }
        );
      }

      // Kiểm tra vector store có tồn tại và thuộc về user hiện tại không
      const vectorStore = await vectorStoreRepository.findOneByIdAndUserId(vectorStoreId, userId);

      if (!vectorStore) {
        throw new AppException(
          AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
          `Vector store với ID ${vectorStoreId} không tồn tại hoặc bạn không có quyền truy cập`
        );
      }

      console.log('Vector store hợp lệ:', vectorStore.name);
    }
  }
}
