import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString } from 'class-validator';

/**
 * DTO cho việc thêm media vào agent
 */
export class AddAgentMediaDto {
  /**
   * <PERSON>h sách ID của media
   */
  @ApiProperty({
    description: 'Danh sách ID của media',
    example: ['m1e2d3i4a5-1', 'm1e2d3i4a5-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  mediaIds: string[];
}
