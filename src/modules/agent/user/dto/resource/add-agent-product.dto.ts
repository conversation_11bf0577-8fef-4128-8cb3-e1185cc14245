import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString } from 'class-validator';

/**
 * DTO cho việc thêm sản phẩm vào agent
 */
export class AddAgentProductDto {
  /**
   * <PERSON>h sách ID của sản phẩm
   */
  @ApiProperty({
    description: 'Danh sách ID của sản phẩm',
    example: ['p1r2o3d4-1', 'p1r2o3d4-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  productIds: string[];
}
