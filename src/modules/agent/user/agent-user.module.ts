import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { MediaModule } from '@modules/data/media/media.module';
import { UrlModule } from '@modules/data/url/url.module';
import { MarketplaceModule } from '@modules/marketplace/marketplace.module';
import { ModelTrainingModule } from '@modules/model-training/model-training.module';
import { ApiKeyEncryptionHelper } from '@modules/model-training/helpers/api-key-encryption.helper';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { BaseModel, FineTuningModel } from '@modules/model-training/entities';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { S3Service } from '@shared/services/s3.service';
import { FacebookService } from '@shared/services/facebook/facebook.service';
import { ToolsModule } from '@/modules/tools/tools.module';
import { ToolsUserModule } from '@/modules/tools/user/tools-user.module';

import { StrategyUserModule } from '@modules/strategy/user/strategy-user.module';
import { BusinessUserModule } from '@modules/business/user/business-user.module';
import { IntegrationModule } from '@modules/integration/integration.module';
import { UserWebsiteRepository, FacebookPageRepository } from '@modules/integration/repositories';
import {
  Agent,
  AgentMedia,
  AgentProduct,
  AgentUrl,
  AgentUser,

  TypeAgent,
  UserMultiAgent
} from '@modules/agent/entities';
import {
  AgentRepository,
  AgentUserRepository,
  TypeAgentRepository,
  AgentMediaRepository,
  AgentUrlRepository,
  AgentProductRepository,
  AgentRankRepository,
  UserMultiAgentRepository
} from '@modules/agent/repositories';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import { UserProviderModelRepository, BaseModelRepository, FineTuningModelRepository } from '@modules/model-training/repositories';
import {
  TypeAgentUserController,
  AgentUserController,
  AgentResourceUserController,
  AgentStrategyUserController,
  AgentFacebookPageController,
  AgentWebsiteController
} from './controllers';
import {
  TypeAgentUserService,
  AgentUserService,
  AgentResourceUserService,
  AgentStrategyService,
  AgentFacebookPageService,
  AgentWebsiteService
} from './services';
import { ModelValidationHelper } from './helpers/model-validation.helper';
import { TypeAgentValidationHelper } from './helpers/type-agent-validation.helper';
import { ModelResolutionHelper } from './helpers/model-resolution.helper';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([
      UserProduct,
      Agent,
      AgentUser,
      AgentMedia,
      AgentUrl,
      AgentProduct,
      TypeAgent,
      UserMultiAgent,
      BaseModel,
      FineTuningModel
    ]),
    HttpModule,
    MediaModule,
    UrlModule,
    MarketplaceModule,
    ToolsModule,
    ToolsUserModule,
    ModelTrainingModule,
    KnowledgeFilesModule,
    ConfigModule,
    StrategyUserModule,
    BusinessUserModule,
    IntegrationModule,
  ],
  controllers: [
    TypeAgentUserController,
    AgentUserController,
    AgentResourceUserController,
    AgentStrategyUserController,
    AgentFacebookPageController,
    AgentWebsiteController,
  ],
  providers: [
    // Services
    TypeAgentUserService,
    AgentUserService,
    AgentResourceUserService,
    AgentStrategyService,
    AgentFacebookPageService,
    AgentWebsiteService,

    // External services
    OpenAiService,
    S3Service,
    FacebookService,
    ApiKeyEncryptionHelper,
    RagFileProcessingService,

    // Helpers
    ModelValidationHelper,
    TypeAgentValidationHelper,
    ModelResolutionHelper,

    // Repositories
    TypeAgentRepository,
    AgentRepository,
    AgentUserRepository,
    AgentMediaRepository,
    AgentUrlRepository,
    AgentProductRepository,
    VectorStoreRepository,
    UserProviderModelRepository,
    BaseModelRepository,
    FineTuningModelRepository,
    MediaRepository,
    UrlRepository,
    UserWebsiteRepository,
    FacebookPageRepository,
    AgentRankRepository,
    UserMultiAgentRepository,
  ],
  exports: [
    TypeAgentUserService,
    AgentUserService,
    AgentResourceUserService,
    AgentStrategyService,
  ],
})
export class AgentUserModule {}
