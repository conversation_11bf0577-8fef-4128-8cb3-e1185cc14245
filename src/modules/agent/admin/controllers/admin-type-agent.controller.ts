import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseBoolPipe,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { AdminTypeAgentService } from '@modules/agent/admin/services';
import {
  CreateTypeAgentDto,
  TypeAgentQueryDto,
  UpdateTypeAgentDto,
  UpdateTypeAgentStatusDto,
  TypeAgentListItemDto,
  TypeAgentDetailDto,
  DeleteTypeAgentDto,
  TypeAgentTrashItemDto,
  RestoreTypeAgentDto,
  BulkRestoreTypeAgentDto,
} from '../dto/type-agent';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentEmployee } from '@modules/auth/decorators';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@/common';

/**
 * Controller xử lý các endpoint liên quan đến Type Agent cho Admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TYPE_AGENT)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/type-agents')
@ApiExtraModels(
  TypeAgentListItemDto,
  TypeAgentDetailDto,
  ApiResponseDto,
  PaginatedResult,
)
export class AdminTypeAgentController {
  constructor(private readonly adminTypeAgentService: AdminTypeAgentService) {}

  /**
   * Lấy danh sách loại agent với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách loại agent',
    description: 'Lấy danh sách loại agent với phân trang và lọc',
  })
  @ApiOkResponse({
    description: 'Danh sách loại agent',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findAll(
    @Query() queryDto: TypeAgentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentListItemDto>>> {
    const result = await this.adminTypeAgentService.findAll(queryDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách loại agent đã xóa mềm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent đã xóa với phân trang
   */
  @Get('trash')
  @ApiOperation({
    summary: 'Lấy danh sách loại agent đã xóa',
    description: 'Lấy danh sách loại agent đã xóa mềm với phân trang và tìm kiếm',
  })
  @ApiOkResponse({
    description: 'Danh sách loại agent đã xóa',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findAllDeleted(
    @Query() queryDto: TypeAgentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentTrashItemDto>>> {
    const result = await this.adminTypeAgentService.findAllDeleted(queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy thông tin chi tiết loại agent theo ID
   * @param id ID của loại agent
   * @returns Thông tin chi tiết loại agent
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết loại agent',
    description: 'Lấy thông tin chi tiết loại agent theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Thông tin chi tiết loại agent',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findOne(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TypeAgentDetailDto>> {
    const result = await this.adminTypeAgentService.findById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo loại agent mới
   * @param createDto Dữ liệu tạo loại agent
   * @param employeeId ID của nhân viên tạo
   * @returns ID của loại agent đã tạo
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo loại agent mới',
    description: 'Tạo loại agent mới với thông tin cung cấp',
  })
  @ApiCreatedResponse({
    description: 'Loại agent đã được tạo thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS,
    AGENT_ERROR_CODES.GROUP_TOOL_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async create(
    @Body() createDto: CreateTypeAgentDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.create(createDto, employeeId);
    return ApiResponseDto.success(null, 'Tạo loại agent thành công');
  }

  /**
   * Cập nhật thông tin loại agent
   * @param id ID của loại agent
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật loại agent',
    description: 'Cập nhật thông tin loại agent theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Loại agent đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS,
    AGENT_ERROR_CODES.GROUP_TOOL_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateTypeAgentDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.update(id, updateDto, employeeId);
    return ApiResponseDto.success(null, 'Cập nhật loại agent thành công');
  }

  /**
   * Cập nhật trạng thái loại agent
   * @param id ID của loại agent
   * @param draft Query param để chuyển đổi DRAFT/APPROVED
   * @param employeeId ID của nhân viên cập nhật
   */
  @Patch(':id/status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái loại agent',
    description: 'Cập nhật trạng thái loại agent theo ID với query param draft=true/false',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiQuery({
    name: 'draft',
    description: 'true để chuyển về DRAFT, false để chuyển về APPROVED',
    type: Boolean,
    example: false,
  })
  @ApiOkResponse({
    description: 'Trạng thái loại agent đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Query('draft', ParseBoolPipe) draft: boolean,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.updateStatusByQuery(id, draft, employeeId);
    return ApiResponseDto.success(null, 'Cập nhật trạng thái loại agent thành công');
  }

  /**
   * Xóa loại agent (soft delete) với migration agents
   * @param id ID của loại agent
   * @param deleteDto Dữ liệu xóa với newTypeAgentId
   * @param employeeId ID của nhân viên xóa
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa loại agent với migration',
    description: 'Xóa loại agent theo ID (soft delete) và chuyển các agents sang type mới',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Loại agent đã được xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @Body() deleteDto: DeleteTypeAgentDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ migratedAgents: number }>> {
    const migratedAgents = await this.adminTypeAgentService.removeWithMigration(id, deleteDto.newTypeAgentId, employeeId);
    return ApiResponseDto.success({ migratedAgents }, 'Xóa loại agent thành công');
  }





  /**
   * Khôi phục loại agent đã xóa mềm
   * @param restoreDto Dữ liệu khôi phục
   * @returns Kết quả khôi phục
   */
  @Post('restore')
  @ApiOperation({
    summary: 'Khôi phục loại agent đã xóa',
    description: 'Khôi phục loại agent đã xóa mềm về trạng thái bình thường',
  })
  @ApiCreatedResponse({
    description: 'Loại agent đã được khôi phục thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async restore(
    @Body() restoreDto: RestoreTypeAgentDto,
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.restore(restoreDto.id);
    return ApiResponseDto.success(null, 'Khôi phục loại agent thành công');
  }

  /**
   * Khôi phục nhiều loại agent đã xóa mềm
   * @param bulkRestoreDto Dữ liệu khôi phục nhiều
   * @returns Kết quả khôi phục
   */
  @Post('restore/bulk')
  @ApiOperation({
    summary: 'Khôi phục nhiều loại agent đã xóa',
    description: 'Khôi phục nhiều loại agent đã xóa mềm về trạng thái bình thường',
  })
  @ApiCreatedResponse({
    description: 'Các loại agent đã được khôi phục thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async bulkRestore(
    @Body() bulkRestoreDto: BulkRestoreTypeAgentDto,
  ): Promise<ApiResponseDto<{ restoredCount: number }>> {
    const restoredCount = await this.adminTypeAgentService.bulkRestore(bulkRestoreDto.ids);
    return ApiResponseDto.success({ restoredCount }, 'Khôi phục nhiều loại agent thành công');
  }
}
