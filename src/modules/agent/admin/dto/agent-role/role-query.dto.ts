import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của vai trò
 */
export enum RoleSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho query parameters khi lấy danh sách vai trò
 */
export class RoleQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Từ khóa tìm kiếm theo tên hoặc mô tả',
    required: false,
    example: 'admin',
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  // Đã loại bỏ trường agentId vì không còn mối quan hệ giữa agent và role

  @ApiProperty({
    description: 'Trường sắp xếp',
    required: false,
    enum: RoleSortBy,
    default: RoleSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(RoleSortBy)
  sortBy?: RoleSortBy = RoleSortBy.CREATED_AT;
}
