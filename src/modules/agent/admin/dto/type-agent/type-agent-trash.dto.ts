import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TypeAgentStatus } from '@modules/agent/constants';
import { EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { IsNumber, IsArray } from 'class-validator';

/**
 * DTO cho thông tin type agent đã bị xóa mềm
 */
export class TypeAgentTrashItemDto {
  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  id: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  name: string;

  /**
   * <PERSON>ô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  description: string | null;

  /**
   * Tr<PERSON>ng thái của loại agent trư<PERSON><PERSON> khi bị xóa
   */
  @ApiProperty({
    description: 'Trạng thái của loại agent tr<PERSON><PERSON><PERSON> khi bị xóa',
    enum: TypeAgentStatus,
    example: TypeAgentStatus.APPROVED,
  })
  status: TypeAgentStatus;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp millis)',
    example: 1682506892000,
  })
  createdAt: number;

  /**
   * Thời gian xóa
   */
  @ApiProperty({
    description: 'Thời gian xóa (timestamp millis)',
    example: 1682506892000,
  })
  deletedAt: number;

  /**
   * Thông tin người tạo
   */
  @ApiPropertyOptional({
    description: 'Thông tin người tạo',
    type: EmployeeInfoDto,
  })
  created?: EmployeeInfoDto;

  /**
   * Thông tin người xóa
   */
  @ApiProperty({
    description: 'Thông tin người xóa',
    type: EmployeeInfoDto,
  })
  deleted: EmployeeInfoDto;

  /**
   * Số lượng tool của loại agent
   */
  @ApiProperty({
    description: 'Số lượng tool của loại agent',
    example: 5,
  })
  countTool: number;
}

/**
 * DTO cho việc xóa type agent với migration
 */
export class DeleteTypeAgentDto {
  /**
   * ID của type agent mới để chuyển các agents hiện tại
   */
  @ApiProperty({
    description: 'ID của type agent mới để chuyển các agents hiện tại',
    example: 2,
  })
  @IsNumber()
  newTypeAgentId: number;
}



/**
 * DTO cho việc khôi phục type agent
 */
export class RestoreTypeAgentDto {
  /**
   * ID của type agent cần khôi phục
   */
  @ApiProperty({
    description: 'ID của type agent cần khôi phục',
    example: 1,
  })
  @IsNumber()
  id: number;
}

/**
 * DTO cho việc khôi phục nhiều type agents
 */
export class BulkRestoreTypeAgentDto {
  /**
   * Danh sách ID của các type agents cần khôi phục
   */
  @ApiProperty({
    description: 'Danh sách ID của các type agents cần khôi phục',
    example: [1, 3, 5],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  ids: number[];
}
