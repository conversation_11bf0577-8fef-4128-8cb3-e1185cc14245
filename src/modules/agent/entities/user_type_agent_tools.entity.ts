import { Entity, PrimaryColumn, ManyToOne, JoinColumn } from 'typeorm';
import { TypeAgent } from './type-agent.entity';
import { UserTool } from '@modules/tools/entities';

/**
 * Entity đại diện cho bảng user_type_agent_tools trong cơ sở dữ liệu
 * Bảng liên kết ánh xạ user tools với type agent
 * Hiện tại không sử dụng vì chỉ admin tạo type-agent
 */
@Entity('user_type_agent_tools')
export class UserTypeAgentTools {
  /**
   * ID của user tool (UUID)
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'tool_id', type: 'uuid' })
  toolId: string;

  /**
   * ID của type agent
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'type_id', type: 'integer' })
  typeId: number;

  /**
   * <PERSON>uan hệ với TypeAgent
   */
  @ManyToOne(() => TypeAgent)
  @JoinColumn({ name: 'type_id' })
  typeAgent: TypeAgent;

  /**
   * <PERSON>uan hệ với UserTool
   */
  @ManyToOne(() => UserTool)
  @JoinColumn({ name: 'tool_id' })
  tool: UserTool;
}
