import {Entity, PrimaryColumn} from 'typeorm';

/**
 * Entity đại diện cho bảng agents_url trong cơ sở dữ liệu
 * Bảng quan hệ nhiều-nhiều giữa agents và url_data
 */
@Entity('agents_url')
export class AgentUrl {
  /**
   * ID của url_data, là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'url_id', type: 'uuid' })
  urlId: string;

  /**
   * ID của agent, là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'agent_id', type: 'uuid' })
  agentId: string;
}
