import { TypeAgentConfig } from '../interfaces/type-agent-config.interface';

/**
 * Utility để tạo cấu hình mặc định cho TypeAgent
 */
export class TypeAgentConfigUtil {
  /**
   * Tạo cấu hình mặc định với tất cả các trường false
   */
  static createDefaultConfig(): TypeAgentConfig {
    return {
      hasProfile: false,
      hasOutput: false,
      hasConversion: false,
      hasResources: false,
      hasStrategy: false,
      hasMultiAgent: false,
    };
  }

  /**
   * Tạo cấu hình cơ bản (profile, output, resources)
   */
  static createBasicConfig(): TypeAgentConfig {
    return {
      hasProfile: true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
      hasStrategy: false,
      hasMultiAgent: false,
    };
  }

  /**
   * Tạo cấu hình đầy đủ với tất cả các tính năng
   */
  static createFullConfig(): TypeAgentConfig {
    return {
      hasProfile: true,
      hasOutput: true,
      hasConversion: true,
      hasResources: true,
      hasStrategy: true,
      hasMultiAgent: true,
    };
  }

  /**
   * Validate cấu hình TypeAgent
   * @param config Cấu hình cần validate
   * @returns true nếu hợp lệ, false nếu không
   */
  static validateConfig(config: any): config is TypeAgentConfig {
    if (!config || typeof config !== 'object') {
      return false;
    }

    const requiredFields: (keyof TypeAgentConfig)[] = [
      'hasProfile',
      'hasOutput', 
      'hasConversion',
      'hasResources',
      'hasStrategy',
      'hasMultiAgent'
    ];

    return requiredFields.every(field => 
      field in config && typeof config[field] === 'boolean'
    );
  }

  /**
   * Merge cấu hình với cấu hình mặc định
   * @param config Cấu hình partial
   * @returns Cấu hình đầy đủ
   */
  static mergeWithDefault(config: Partial<TypeAgentConfig>): TypeAgentConfig {
    const defaultConfig = this.createDefaultConfig();
    return {
      ...defaultConfig,
      ...config,
    };
  }

  /**
   * Lấy danh sách tất cả các trường trong TypeAgentConfig
   * Hữu ích để tự động tạo form hoặc validation
   */
  static getConfigFields(): (keyof TypeAgentConfig)[] {
    return [
      'hasProfile',
      'hasOutput',
      'hasConversion', 
      'hasResources',
      'hasStrategy',
      'hasMultiAgent'
    ];
  }

  /**
   * Lấy mô tả cho từng trường
   */
  static getFieldDescriptions(): Record<keyof TypeAgentConfig, string> {
    return {
      hasProfile: 'Có hồ sơ không - Cho phép agent có thông tin hồ sơ cá nhân',
      hasOutput: 'Có đầu ra không - Cho phép agent tạo ra các output/kết quả',
      hasConversion: 'Có chuyển đổi không - Cho phép agent thực hiện các conversion/chuyển đổi dữ liệu',
      hasResources: 'Có tài nguyên không - Cho phép agent sử dụng các resources/tài nguyên',
      hasStrategy: 'Có chiến lược không - Cho phép agent sử dụng các strategy/chiến lược',
      hasMultiAgent: 'Có đa agent không - Cho phép agent làm việc với nhiều agent khác',
    };
  }
}
