import { Column, Entity, Index, Join<PERSON>olumn, <PERSON>ToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Employee } from '@modules/employee/entities/employee.entity';

/**
 * Enum định nghĩa trạng thái của cấp bậc chiến lược
 */
export enum RankStrategyStatus {
  ENABLED = 'APPROVED',
  DISABLED = 'DELETED'
}

/**
 * Entity đại diện cho bảng rank_strategy trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin về các cấp bậc trong chiến lược
 */
@Entity('rank_strategy')
export class RankStrategy {
  /**
   * ID duy nhất của cấp bậc
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'integer' })
  id: number;

  /**
   * Đường dẫn hình ảnh đại diện cho cấp bậc
   */
  @Column({ name: 'image', length: 255 })
  image: string;

  /**
   * Tên củ<PERSON> cấp bậc
   */
  @Column({ name: 'name', length: 255 })
  name: string;

  /**
   * <PERSON><PERSON> tả chi tiết về cấp bậc
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  @Index('idx_rank_s3key')
  description: string;

  /**
   * Trạng thái của cấp bậc chiến lược
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: RankStrategyStatus,
    default: RankStrategyStatus.ENABLED
  })
  status: RankStrategyStatus;

  /**
   * Thời gian tạo (timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint' })
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint' })
  updatedAt: number;

  /**
   * ID của nhân viên tạo cấp bậc
   */
  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật cấp bậc
   */
  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;
}
