import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { StrategyStatusEnum } from '../../constants/strategy-status.enum';

/**
 * DTO cho việc cập nhật Strategy Agent
 */
export class UpdateStrategyAgentDto {
  @ApiProperty({
    description: 'Tên của chiến lược xử lý',
    example: 'Strategy Marketing 2023',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên chiến lược phải là chuỗi' })
  name?: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về chiến lược xử lý',
    example: 'Chiến lược hướng dẫn khách hàng về sản phẩm mới phiên bản 2023',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> tả phải là chuỗi' })
  description?: string;

  @ApiProperty({
    description: 'ID của cấp bậc chiến lượ<PERSON>',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID cấp bậc phải là số' })
  @Type(() => Number)
  rankId?: number;

  @ApiProperty({
    description: 'Danh sách các tag dùng để phân loại và tìm kiếm chiến lược',
    example: ['marketing', 'sales', 'product', '2023'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[];

  @ApiProperty({
    description: 'Trạng thái hoạt động của chiến lược',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trạng thái hoạt động phải là boolean' })
  isActive?: boolean;
}
