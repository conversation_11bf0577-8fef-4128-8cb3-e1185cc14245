import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { StrategyAgentVersion, StrategyContentStep } from '@modules/strategy/entities';
import { StrategyStatusEnum } from '@modules/strategy/constants/strategy-status.enum';
import { QueryStrategyAgentVersionDto } from '../admin/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Transactional } from 'typeorm-transactional';

/**
 * Repository cho entity StrategyAgentVersion
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến phiên bản chiến lược agent
 */
@Injectable()
export class StrategyAgentVersionRepository extends Repository<StrategyAgentVersion> {
  constructor(private dataSource: DataSource) {
    super(StrategyAgentVersion, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho StrategyAgentVersion
   * @returns SelectQueryBuilder cho StrategyAgentVersion
   */
  private createBaseQuery(): SelectQueryBuilder<StrategyAgentVersion> {
    return this.createQueryBuilder('version');
  }

  /**
   * Tìm kiếm danh sách phiên bản Strategy Agent với phân trang
   * @param strategyAgentId ID của Strategy Agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách phiên bản Strategy Agent với phân trang
   */
  async findPaginated(strategyAgentId: string, queryDto: QueryStrategyAgentVersionDto): Promise<PaginatedResult<StrategyAgentVersion>> {
    const { page = 1, limit = 10, sortBy = 'versionNumber', sortDirection = 'DESC', status } = queryDto;

    const fields = ['id', 'strategyAgentId', 'versionNumber', 'versionName', 'status', 'createdAt', 'updatedAt'];

    const qb = this.createBaseQuery()
      .select(fields.map(field => `version.${field}`))
      .where('version.strategy_agent_id = :strategyAgentId', { strategyAgentId });

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('version.status = :status', { status });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`version.${sortBy}`, sortDirection as 'ASC' | 'DESC');

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Tìm phiên bản Strategy Agent theo ID
   * @param id ID của phiên bản Strategy Agent
   * @returns Phiên bản Strategy Agent hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<StrategyAgentVersion | null> {
    const fields = [
      'id', 'strategyAgentId', 'versionNumber', 'versionName', 'status',
      'modelId', 'modelConfig', 'systemPrompt', 'changeDescription', 
      'createdAt', 'updatedAt', 'createdBy', 'updatedBy'
    ];

    return this.createBaseQuery()
      .select(fields.map(field => `version.${field}`))
      .where('version.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm phiên bản Strategy Agent theo ID của Strategy Agent và số phiên bản
   * @param strategyAgentId ID của Strategy Agent
   * @param versionNumber Số phiên bản
   * @returns Phiên bản Strategy Agent hoặc null nếu không tìm thấy
   */
  async findByVersionNumber(strategyAgentId: string, versionNumber: number): Promise<StrategyAgentVersion | null> {
    const fields = [
      'id', 'strategyAgentId', 'versionNumber', 'versionName', 'status',
      'modelId', 'modelConfig', 'systemPrompt', 'changeDescription', 
      'createdAt', 'updatedAt', 'createdBy', 'updatedBy'
    ];

    return this.createBaseQuery()
      .select(fields.map(field => `version.${field}`))
      .where('version.strategy_agent_id = :strategyAgentId', { strategyAgentId })
      .andWhere('version.version_number = :versionNumber', { versionNumber })
      .getOne();
  }

  /**
   * Tìm phiên bản theo ID chiến lược
   * @param strategyId ID của chiến lược
   * @returns Danh sách phiên bản
   */
  async findByStrategyId(strategyId: string): Promise<StrategyAgentVersion[]> {
    return this.createBaseQuery()
      .where('version.strategy_agent_id = :strategyId', { strategyId })
      .andWhere('version.status != :status', { status: StrategyStatusEnum.DELETE })
      .orderBy('version.version_number', 'DESC')
      .getMany();
  }

  /**
   * Tìm phiên bản mới nhất của chiến lược
   * @param strategyId ID của chiến lược
   * @returns Phiên bản mới nhất
   */
  async findLatestVersion(strategyId: string): Promise<StrategyAgentVersion | null> {
    return this.createBaseQuery()
      .where('version.strategy_agent_id = :strategyId', { strategyId })
      .andWhere('version.status != :status', { status: StrategyStatusEnum.DELETE })
      .orderBy('version.version_number', 'DESC')
      .getOne();
  }

  /**
   * Cập nhật phiên bản Strategy Agent
   * @param id ID của phiên bản Strategy Agent
   * @param updateData Dữ liệu cập nhật
   * @returns Số bản ghi đã cập nhật
   */
  @Transactional()
  async updateStrategyAgentVersion(id: number, updateData: Partial<StrategyAgentVersion>): Promise<number> {
    const result = await this.createQueryBuilder()
      .update(StrategyAgentVersion)
      .set({
        ...updateData,
        updatedAt: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
      })
      .where('id = :id', { id })
      .execute();

    return result.affected || 0;
  }

  /**
   * Đặt phiên bản mặc định cho Strategy Agent
   * @param strategyAgentId ID của Strategy Agent
   * @param versionId ID của phiên bản được đặt làm mặc định
   */
  @Transactional()
  async setDefaultVersion(strategyAgentId: string, versionId: number): Promise<void> {
    // Đặt tất cả các phiên bản khác của Strategy Agent này thành DRAFT
    await this.createQueryBuilder()
      .update(StrategyAgentVersion)
      .set({ status: StrategyStatusEnum.DRAFT })
      .where('strategy_agent_id = :strategyAgentId', { strategyAgentId })
      .execute();

    // Đặt phiên bản được chỉ định thành APPROVED
    await this.createQueryBuilder()
      .update(StrategyAgentVersion)
      .set({ status: StrategyStatusEnum.APPROVED })
      .where('id = :versionId', { versionId })
      .andWhere('strategy_agent_id = :strategyAgentId', { strategyAgentId })
      .execute();
  }
}
