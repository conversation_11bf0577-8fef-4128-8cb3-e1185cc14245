/**
 * Constants cho GHTK API
 */

/**
 * Base URLs cho GHTK API
 */
export const GHTK_BASE_URLS = {
  TEST: 'https://services.ghtk.vn',
  PRODUCTION: 'https://services.ghtk.vn'
} as const;

/**
 * GHTK API Endpoints
 */
export const GHTK_ENDPOINTS = {
  // Giải pháp
  GET_SOLUTIONS: '/open/api/v1/shop/solutions',
  
  // Đơn hàng
  CREATE_ORDER: '/services/shipment/order',
  CALCULATE_FEE: '/services/shipment/fee',
  GET_ORDER_STATUS: '/services/shipment/v2',
  PRINT_LABEL: '/services/label',
  CANCEL_ORDER: '/services/shipment/cancel',
  
  // Địa chỉ
  GET_PICKUP_ADDRESSES: '/services/shipment/list_pick_add',
  GET_LEVEL4_ADDRESS: '/services/address/getAddressLevel4',
  
  // Sản phẩm
  SEARCH_PRODUCTS: '/services/kho-hang/thong-tin-san-pham'
} as const;

/**
 * GHTK Default Headers
 */
export const GHTK_DEFAULT_HEADERS = {
  'Content-Type': 'application/json'
} as const;

/**
 * GHTK Test Configuration
 * Sử dụng token test từ tài liệu GHTK
 */
export const GHTK_TEST_CONFIG = {
  TOKEN: 'APITokenSample-ca441e70288cB0515F310742',
  PARTNER_CODE: 'PARTNER_CODE_SAMPLE',
  BASE_URL: GHTK_BASE_URLS.TEST
} as const;



/**
 * GHTK Transport Methods
 */
export const GHTK_TRANSPORT_METHODS = {
  ROAD: 'road',
  FLY: 'fly'
} as const;

/**
 * GHTK Pick Options
 */
export const GHTK_PICK_OPTIONS = {
  COD: 'cod',
  POST: 'post'
} as const;

/**
 * GHTK Deliver Options
 */
export const GHTK_DELIVER_OPTIONS = {
  XTEAM: 'xteam',
  NONE: 'none'
} as const;

/**
 * GHTK Paper Sizes
 */
export const GHTK_PAPER_SIZES = {
  A5: 'A5',
  A6: 'A6'
} as const;

/**
 * GHTK Label Orientations
 */
export const GHTK_LABEL_ORIENTATIONS = {
  PORTRAIT: 'portrait',
  LANDSCAPE: 'landscape'
} as const;

/**
 * GHTK Tags (Nhãn đơn hàng)
 * Lưu ý: Từ 01/01/2026 GHTK ngừng hỗ trợ tags, thay bằng giải pháp
 */
export const GHTK_TAGS = {
  FRAGILE: 1,             // Hàng dễ vỡ
  HIGH_VALUE: 2,          // Giá trị cao
  LIQUID: 3,              // Chất lỏng
  FOOD: 4,                // Thực phẩm
  ELECTRONICS: 5,         // Điện tử
  DOCUMENTS: 6,           // Tài liệu
  CLOTHING: 7,            // Quần áo
  COSMETICS: 8,           // Mỹ phẩm
  MEDICINE: 9,            // Thuốc
  BOOKS: 10               // Sách
} as const;

/**
 * GHTK Error Messages
 */
export const GHTK_ERROR_MESSAGES = {
  INVALID_TOKEN: 'Token không hợp lệ',
  INVALID_PARTNER_CODE: 'Mã đối tác không hợp lệ',
  ORDER_NOT_FOUND: 'Không tìm thấy đơn hàng',
  CANNOT_CANCEL: 'Không thể hủy đơn hàng',
  INVALID_ADDRESS: 'Địa chỉ không hợp lệ',
  INVALID_WEIGHT: 'Trọng lượng không hợp lệ',
  INVALID_VALUE: 'Giá trị đơn hàng không hợp lệ',
  API_ERROR: 'Lỗi khi gọi API GHTK',
  NETWORK_ERROR: 'Lỗi kết nối mạng',
  TIMEOUT_ERROR: 'Timeout khi gọi API'
} as const;

/**
 * GHTK Request Timeout (milliseconds)
 */
export const GHTK_TIMEOUT = 30000; // 30 seconds

/**
 * GHTK Webhook Events
 */
export const GHTK_WEBHOOK_EVENTS = {
  ORDER_STATUS_UPDATED: 'order_status_updated'
} as const;
