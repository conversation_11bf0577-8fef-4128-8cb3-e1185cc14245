# GHN Environment Configuration

## Environment Variables

Đ<PERSON> sử dụng GHN Service, bạn cần cấu hình các environment variables sau:

### Required Variables

```bash
# GHN API Token (bắt buộc)
GHN_TOKEN=your_ghn_api_token_here

# GHN Shop ID (bắt buộc)
GHN_SHOP_ID=your_shop_id_here
```

### Optional Variables

```bash
# GHN Base URL (mặc định: https://dev-online-gateway.ghn.vn)
GHN_BASE_URL=https://dev-online-gateway.ghn.vn

# GHN Request Timeout in milliseconds (mặc định: 30000)
GHN_TIMEOUT=30000

# Test Mode (mặc định: true)
GHN_TEST_MODE=true
```

## Cấu hình cho các môi trường

### Development (.env.development)

```bash
# GHN Test Configuration
GHN_TOKEN=your_real_ghn_token_here
GHN_SHOP_ID=your_real_shop_id_here
GHN_BASE_URL=https://dev-online-gateway.ghn.vn
GHN_TIMEOUT=30000
GHN_TEST_MODE=true
```

### Staging (.env.staging)

```bash
# GHN Staging Configuration
GHN_TOKEN=your_staging_token_here
GHN_SHOP_ID=your_staging_shop_id_here
GHN_BASE_URL=https://dev-online-gateway.ghn.vn
GHN_TIMEOUT=30000
GHN_TEST_MODE=true
```

### Production (.env.production)

```bash
# GHN Production Configuration
GHN_TOKEN=your_production_token_here
GHN_SHOP_ID=your_production_shop_id_here
GHN_BASE_URL=https://online-gateway.ghn.vn
GHN_TIMEOUT=30000
GHN_TEST_MODE=false
```

## Lấy Token và Shop ID GHN

### 1. Đăng ký tài khoản GHN

1. Truy cập [https://sso.ghn.vn/](https://sso.ghn.vn/)
2. Đăng ký tài khoản doanh nghiệp
3. Xác thực thông tin và tài liệu

### 2. Tạo cửa hàng và lấy thông tin API

1. Đăng nhập vào tài khoản GHN
2. Vào mục **Quản lý cửa hàng** > **Tạo cửa hàng**
3. Điền thông tin cửa hàng và địa chỉ lấy hàng
4. Sau khi tạo thành công, vào **Cài đặt** > **Thông tin API**
5. Copy **Token** và **Shop ID**

### 3. Môi trường Test vs Production

**Môi trường Test (Sandbox):**
- Base URL: `https://dev-online-gateway.ghn.vn`
- Sử dụng token và shop ID test
- Không tạo đơn hàng thực tế
- Dùng cho development và testing

**Môi trường Production:**
- Base URL: `https://online-gateway.ghn.vn`
- Sử dụng token và shop ID thực
- Tạo đơn hàng thực tế và tính phí
- Dùng cho production

## Validation

Service sẽ tự động validate cấu hình khi khởi tạo:

```typescript
this.logger.log('GHN Service initialized', {
  baseUrl: this.config.baseUrl,
  isTestMode: this.config.isTestMode,
  hasToken: !!this.config.token,
  hasShopId: !!this.config.shopId
});
```

## Fallback Values

Nếu không có environment variables, service sẽ sử dụng giá trị mặc định:

```typescript
{
  token: GHN_TEST_CONFIG.TOKEN,
  shopId: GHN_TEST_CONFIG.SHOP_ID,
  baseUrl: GHN_TEST_CONFIG.BASE_URL,
  timeout: GHN_TIMEOUT,
  isTestMode: true
}
```

## Security Best Practices

### 1. Không commit token vào git

Thêm vào `.gitignore`:

```gitignore
# Environment files
.env
.env.local
.env.development
.env.staging
.env.production
```

### 2. Sử dụng secret management

Trong production, sử dụng:
- AWS Secrets Manager
- Azure Key Vault
- Google Secret Manager
- Kubernetes Secrets

### 3. Rotate tokens định kỳ

- Thay đổi token GHN định kỳ
- Update environment variables
- Restart services

## Docker Configuration

### docker-compose.yml

```yaml
version: '3.8'
services:
  app:
    build: .
    environment:
      - GHN_TOKEN=${GHN_TOKEN}
      - GHN_SHOP_ID=${GHN_SHOP_ID}
      - GHN_BASE_URL=${GHN_BASE_URL}
      - GHN_TIMEOUT=${GHN_TIMEOUT}
      - GHN_TEST_MODE=${GHN_TEST_MODE}
    env_file:
      - .env
```

### Dockerfile

```dockerfile
# Environment variables can be set at build time
ARG GHN_TOKEN
ARG GHN_SHOP_ID

ENV GHN_TOKEN=${GHN_TOKEN}
ENV GHN_SHOP_ID=${GHN_SHOP_ID}
```

## Kubernetes Configuration

### ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ghn-config
data:
  GHN_BASE_URL: "https://online-gateway.ghn.vn"
  GHN_TIMEOUT: "30000"
  GHN_TEST_MODE: "false"
```

### Secret

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: ghn-secret
type: Opaque
data:
  GHN_TOKEN: <base64-encoded-token>
  GHN_SHOP_ID: <base64-encoded-shop-id>
```

### Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
spec:
  template:
    spec:
      containers:
      - name: app
        image: your-app:latest
        envFrom:
        - configMapRef:
            name: ghn-config
        - secretRef:
            name: ghn-secret
```

## Monitoring & Alerts

### Health Check

Service tự động log trạng thái cấu hình:

```typescript
// Kiểm tra token có hợp lệ không
if (!this.config.token) {
  this.logger.warn('GHN token not configured, using test token');
}

// Kiểm tra shop ID
if (!this.config.shopId) {
  this.logger.warn('GHN shop ID not configured');
}
```

### Alerts

Thiết lập alerts cho:
- Token sắp hết hạn
- API rate limit exceeded
- Connection failures
- Invalid configuration

## Troubleshooting

### Common Issues

1. **Token không hợp lệ (HTTP 401)**
   ```
   Error: GHN_INVALID_TOKEN - Token GHN không hợp lệ
   ```
   **Giải pháp:**
   - Kiểm tra token trong environment variables
   - Verify token trên GHN dashboard
   - Đảm bảo token chưa hết hạn

2. **Shop ID sai**
   ```
   Error: GHN_INVALID_SHOP_ID - Shop ID GHN không hợp lệ
   ```
   - Kiểm tra shop ID
   - Đảm bảo shop đã được kích hoạt
   - Liên hệ GHN support

3. **Network issues**
   ```
   Error: GHN_NETWORK_ERROR - Lỗi kết nối mạng với GHN
   ```
   - Kiểm tra internet connection
   - Verify GHN API status
   - Kiểm tra firewall settings

4. **Môi trường sai**
   ```
   Error: Không thể tạo đơn hàng
   ```
   - Kiểm tra `GHN_TEST_MODE` setting
   - Đảm bảo sử dụng đúng base URL
   - Verify token và shop ID cho môi trường tương ứng

### Debug Mode

Enable debug logging:

```bash
LOG_LEVEL=debug
GHN_TEST_MODE=true
```

Service sẽ log chi tiết tất cả requests/responses.

## API Limits

### Rate Limits
- GHN có giới hạn số lượng request per minute
- Implement retry logic với exponential backoff
- Monitor API usage

### Data Limits
- Khối lượng tối đa: 50kg
- Kích thước tối đa: 200cm mỗi chiều
- COD tối đa: 50,000,000 VND
- Giá trị bảo hiểm tối đa: 5,000,000 VND

## Support

### GHN Support
- Hotline: 1900 636 677
- Email: <EMAIL>
- Website: https://ghn.vn

### Documentation
- API Documentation: https://api.ghn.vn/home/<USER>/detail
- Developer Portal: https://ghn.vn/pages/dev
