import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserOrder, UserConvertCustomer, UserProduct } from '../entities';
import {
  ReportOverviewQueryDto,
  SalesChartQueryDto,
  OrdersChartQueryDto,
  CustomersChartQueryDto,
  TopSellingProductsQueryDto,
  ProductsChartQueryDto,
  PotentialCustomersQueryDto,
  ChartGroupByEnum
} from '../user/dto/report';

/**
 * Repository xử lý các truy vấn báo cáo business
 */
@Injectable()
export class BusinessReportRepository {
  private readonly logger = new Logger(BusinessReportRepository.name);

  constructor(
    @InjectRepository(UserOrder)
    private readonly userOrderRepository: Repository<UserOrder>,
    @InjectRepository(UserConvertCustomer)
    private readonly userConvertCustomerRepository: Repository<UserConvertCustomer>,
    @InjectRepository(UserProduct)
    private readonly userProductRepository: Repository<UserProduct>,
  ) {}

  /**
   * Lấy dữ liệu tổng quan báo cáo
   */
  async getReportOverview(userId: number, query: ReportOverviewQueryDto) {
    try {
      const { startDate, endDate } = this.getDateRange(query.startDate, query.endDate);

      // Tính toán doanh thu và đơn hàng
      const revenueQuery = this.userOrderRepository
        .createQueryBuilder('user_order')
        .select([
          'COUNT(user_order.id) as total_orders',
          'COALESCE(SUM(CAST("user_order".bill_info->>\'totalAmount\' AS DECIMAL)), 0) as total_revenue'
        ])
        .where('user_order.user_id = :userId', { userId })
        .andWhere('user_order.created_at >= :startDate', { startDate })
        .andWhere('user_order.created_at <= :endDate', { endDate });

      // Tính toán khách hàng mới
      const customersQuery = this.userConvertCustomerRepository
        .createQueryBuilder('customer')
        .select('COUNT(customer.id) as new_customers')
        .where('customer.user_id = :userId', { userId })
        .andWhere('customer.created_at >= :startDate', { startDate })
        .andWhere('customer.created_at <= :endDate', { endDate });

      const [revenueResult, customersResult] = await Promise.all([
        revenueQuery.getRawOne(),
        customersQuery.getRawOne()
      ]);

      return {
        totalRevenue: parseFloat(revenueResult?.total_revenue || '0'),
        totalOrders: parseInt(revenueResult?.total_orders || '0'),
        newCustomers: parseInt(customersResult?.new_customers || '0'),
        startDate: this.formatDate(startDate),
        endDate: this.formatDate(endDate)
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu tổng quan: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy dữ liệu biểu đồ doanh thu
   */
  async getSalesChartData(userId: number, query: SalesChartQueryDto) {
    try {
      const { startDate, endDate } = this.getDateRange(query.startDate, query.endDate);
      const groupByFormat = this.getGroupByFormat(query.groupBy || ChartGroupByEnum.MONTH, 'user_order');

      const queryBuilder = this.userOrderRepository
        .createQueryBuilder('user_order')
        .select([
          `${groupByFormat} as period`,
          'COUNT(user_order.id) as orders',
          'COALESCE(SUM(CAST("user_order".bill_info->>\'totalAmount\' AS DECIMAL)), 0) as revenue',
          'COALESCE(AVG(CAST("user_order".bill_info->>\'totalAmount\' AS DECIMAL)), 0) as average_order_value'
        ])
        .where('user_order.user_id = :userId', { userId })
        .andWhere('user_order.created_at >= :startDate', { startDate })
        .andWhere('user_order.created_at <= :endDate', { endDate })
        .groupBy('period')
        .orderBy('period', 'ASC');

      const results = await queryBuilder.getRawMany();

      return results.map(result => ({
        period: this.formatPeriodName(result.period, query.groupBy || ChartGroupByEnum.MONTH),
        date: this.formatPeriodDate(result.period, query.groupBy || ChartGroupByEnum.MONTH),
        revenue: parseFloat(result.revenue || '0'),
        orders: parseInt(result.orders || '0'),
        averageOrderValue: parseFloat(result.average_order_value || '0')
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu biểu đồ doanh thu: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy dữ liệu biểu đồ đơn hàng
   */
  async getOrdersChartData(userId: number, query: OrdersChartQueryDto) {
    try {
      const { startDate, endDate } = this.getDateRange(query.startDate, query.endDate);
      const groupByFormat = this.getGroupByFormat(query.groupBy || ChartGroupByEnum.MONTH, 'user_order');

      let queryBuilder = this.userOrderRepository
        .createQueryBuilder('user_order')
        .select([
          `${groupByFormat} as period`,
          'COUNT(user_order.id) as total_orders',
          `COUNT(CASE WHEN user_order.shipping_status = 'pending' THEN 1 END) as pending_orders`,
          `COUNT(CASE WHEN user_order.shipping_status = 'confirmed' THEN 1 END) as confirmed_orders`,
          `COUNT(CASE WHEN user_order.shipping_status = 'shipping' THEN 1 END) as shipping_orders`,
          `COUNT(CASE WHEN user_order.shipping_status = 'delivered' THEN 1 END) as delivered_orders`,
          `COUNT(CASE WHEN user_order.shipping_status = 'cancelled' THEN 1 END) as cancelled_orders`
        ])
        .where('user_order.user_id = :userId', { userId })
        .andWhere('user_order.created_at >= :startDate', { startDate })
        .andWhere('user_order.created_at <= :endDate', { endDate });

      if (query.status) {
        queryBuilder = queryBuilder.andWhere('user_order.shipping_status = :status', { status: query.status });
      }

      const results = await queryBuilder
        .groupBy('period')
        .orderBy('period', 'ASC')
        .getRawMany();

      return results.map(result => ({
        period: this.formatPeriodName(result.period, query.groupBy || ChartGroupByEnum.MONTH),
        date: this.formatPeriodDate(result.period, query.groupBy || ChartGroupByEnum.MONTH),
        totalOrders: parseInt(result.total_orders || '0'),
        pendingOrders: parseInt(result.pending_orders || '0'),
        confirmedOrders: parseInt(result.confirmed_orders || '0'),
        shippingOrders: parseInt(result.shipping_orders || '0'),
        deliveredOrders: parseInt(result.delivered_orders || '0'),
        cancelledOrders: parseInt(result.cancelled_orders || '0')
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu biểu đồ đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy dữ liệu biểu đồ khách hàng
   */
  async getCustomersChartData(userId: number, query: CustomersChartQueryDto) {
    try {
      const { startDate, endDate } = this.getDateRange(query.startDate, query.endDate);
      const groupByFormat = this.getGroupByFormat(query.groupBy || ChartGroupByEnum.MONTH, 'customer');

      const queryBuilder = this.userConvertCustomerRepository
        .createQueryBuilder('customer')
        .select([
          `${groupByFormat} as period`,
          'COUNT(customer.id) as new_customers'
        ])
        .where('customer.user_id = :userId', { userId })
        .andWhere('customer.created_at >= :startDate', { startDate })
        .andWhere('customer.created_at <= :endDate', { endDate })
        .groupBy('period')
        .orderBy('period', 'ASC');

      const results = await queryBuilder.getRawMany();

      return results.map(result => ({
        period: this.formatPeriodName(result.period, query.groupBy || ChartGroupByEnum.MONTH),
        date: this.formatPeriodDate(result.period, query.groupBy || ChartGroupByEnum.MONTH),
        newCustomers: parseInt(result.new_customers || '0'),
        returningCustomers: 0, // TODO: Implement logic for returning customers
        totalCustomers: parseInt(result.new_customers || '0'),
        vipCustomers: 0 // TODO: Implement VIP customer logic
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu biểu đồ khách hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Utility methods
   */
  private getDateRange(startDate?: string, endDate?: string) {
    const now = new Date();
    const start = startDate ? new Date(startDate).getTime() : new Date(now.getFullYear(), 0, 1).getTime();
    const end = endDate ? new Date(endDate).getTime() + 24 * 60 * 60 * 1000 - 1 : now.getTime();

    return { startDate: start, endDate: end };
  }

  private getGroupByFormat(groupBy: ChartGroupByEnum, tableAlias: string = 'user_order'): string {
    switch (groupBy) {
      case ChartGroupByEnum.DAY:
        return `TO_CHAR(TO_TIMESTAMP(${tableAlias}.created_at / 1000), 'YYYY-MM-DD')`;
      case ChartGroupByEnum.WEEK:
        return `TO_CHAR(TO_TIMESTAMP(${tableAlias}.created_at / 1000), 'YYYY-"W"WW')`;
      case ChartGroupByEnum.MONTH:
        return `TO_CHAR(TO_TIMESTAMP(${tableAlias}.created_at / 1000), 'YYYY-MM')`;
      case ChartGroupByEnum.QUARTER:
        return `TO_CHAR(TO_TIMESTAMP(${tableAlias}.created_at / 1000), 'YYYY-"Q"Q')`;
      default:
        return `TO_CHAR(TO_TIMESTAMP(${tableAlias}.created_at / 1000), 'YYYY-MM')`;
    }
  }

  private formatPeriodName(period: string, groupBy: ChartGroupByEnum): string {
    switch (groupBy) {
      case ChartGroupByEnum.DAY:
        return period;
      case ChartGroupByEnum.WEEK:
        return period.replace('W', 'Tuần ');
      case ChartGroupByEnum.MONTH:
        return period.replace('-', '/');
      case ChartGroupByEnum.QUARTER:
        return period.replace('Q', 'Quý ');
      default:
        return period;
    }
  }

  private formatPeriodDate(period: string, groupBy: ChartGroupByEnum): string {
    switch (groupBy) {
      case ChartGroupByEnum.DAY:
        return period;
      case ChartGroupByEnum.WEEK:
        // Convert week format to first day of week
        const [year, week] = period.split('-W');
        const yearNum = parseInt(year || '0', 10);
        const weekNum = parseInt(week || '0', 10);
        const firstDayOfYear = new Date(yearNum, 0, 1);
        const firstDayOfWeek = new Date(firstDayOfYear.getTime() + (weekNum - 1) * 7 * 24 * 60 * 60 * 1000);
        return firstDayOfWeek.toISOString().split('T')[0];
      case ChartGroupByEnum.MONTH:
        return `${period}-01`;
      case ChartGroupByEnum.QUARTER:
        const [qYear, quarter] = period.split('-Q');
        const qYearNum = parseInt(qYear || '0', 10);
        const quarterNum = parseInt(quarter || '0', 10);
        const quarterMonth = (quarterNum - 1) * 3 + 1;
        return `${qYearNum}-${quarterMonth.toString().padStart(2, '0')}-01`;
      default:
        return period;
    }
  }

  /**
   * Lấy dữ liệu sản phẩm bán chạy
   */
  async getTopSellingProducts(userId: number, query: TopSellingProductsQueryDto | ProductsChartQueryDto) {
    try {
      const { startDate, endDate } = this.getDateRange(query.startDate, query.endDate);

      // Sử dụng raw query để xử lý JSONB array elements
      let sql = `
        WITH product_data AS (
          SELECT
            (product_element->>'productId')::bigint as product_id,
            product_element->>'name' as product_name,
            COALESCE(product_element->>'categoryName', 'Chưa phân loại') as category_name,
            product_element->>'imageUrl' as image_url,
            (product_element->>'quantity')::integer as quantity,
            (product_element->>'totalPrice')::decimal as total_price,
            (product_element->>'categoryId')::bigint as category_id,
            uo.id as order_id
          FROM user_orders uo,
               jsonb_array_elements(uo.product_info) as product_element
          WHERE uo.user_id = $1
            AND uo.created_at >= $2
            AND uo.created_at <= $3
            AND uo.product_info IS NOT NULL
            AND jsonb_typeof(uo.product_info) = 'array'
        )
        SELECT
          product_id,
          product_name,
          category_name,
          image_url,
          SUM(quantity) as quantity_sold,
          SUM(total_price) as revenue,
          COUNT(DISTINCT order_id) as orders_count
        FROM product_data
        WHERE product_id IS NOT NULL
      `;

      const params = [userId, startDate, endDate];

      if (query.categoryId) {
        sql += ` AND category_id = $${params.length + 1}`;
        params.push(query.categoryId);
      }

      sql += `
        GROUP BY product_id, product_name, category_name, image_url
      `;

      // Thêm ORDER BY dựa trên sortBy (nếu có)
      const sortBy = (query as any).sortBy || 'revenue';
      switch (sortBy) {
        case 'quantity':
          sql += ` ORDER BY quantity_sold DESC`;
          break;
        case 'orders':
          sql += ` ORDER BY orders_count DESC`;
          break;
        case 'revenue':
        default:
          sql += ` ORDER BY revenue DESC`;
          break;
      }

      sql += ` LIMIT $${params.length + 1}`;
      params.push(query.limit || 10);

      const results = await this.userOrderRepository.query(sql, params);

      return results.map((result: any, index: number) => ({
        rank: index + 1,
        productId: parseInt(result.product_id),
        productName: result.product_name || 'Sản phẩm không tên',
        categoryName: result.category_name || 'Chưa phân loại',
        imageUrl: result.image_url,
        quantitySold: parseInt(result.quantity_sold || '0'),
        revenue: parseFloat(result.revenue || '0'),
        ordersCount: parseInt(result.orders_count || '0'),
        averageRating: null, // TODO: Implement rating system
        growthRate: 0 // TODO: Implement growth rate calculation
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu sản phẩm bán chạy: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy dữ liệu khách hàng tiềm năng
   */
  async getPotentialCustomers(userId: number, query: PotentialCustomersQueryDto) {
    try {
      const queryBuilder = this.userConvertCustomerRepository
        .createQueryBuilder('customer')
        .leftJoin(
          subQuery => {
            return subQuery
              .select([
                'user_order.user_convert_customer_id as customer_id',
                'COUNT(user_order.id) as total_orders',
                'COALESCE(SUM(CAST("user_order".bill_info->>\'totalAmount\' AS DECIMAL)), 0) as total_spent',
                'COALESCE(AVG(CAST("user_order".bill_info->>\'totalAmount\' AS DECIMAL)), 0) as average_order_value',
                'EXTRACT(EPOCH FROM MAX(TO_TIMESTAMP(user_order.created_at / 1000))) * 1000 as last_order_date'
              ])
              .from(UserOrder, 'user_order')
              .where('user_order.user_id = :userId', { userId })
              .groupBy('user_order.user_convert_customer_id');
          },
          'order_stats',
          'customer.id = order_stats.customer_id'
        )
        .select([
          'customer.id as customer_id',
          'customer.name as customer_name',
          'customer.email as email',
          'customer.phone as phone',
          'customer.avatar as avatar_url',
          'COALESCE(order_stats.total_orders, 0) as total_orders',
          'COALESCE(order_stats.total_spent, 0) as total_spent',
          'COALESCE(order_stats.average_order_value, 0) as average_order_value',
          'order_stats.last_order_date as last_order_date',
          'CASE WHEN order_stats.last_order_date IS NOT NULL THEN EXTRACT(EPOCH FROM NOW() - TO_TIMESTAMP(order_stats.last_order_date / 1000)) / 86400 ELSE NULL END as days_since_last_order'
        ])
        .where('customer.user_id = :userId', { userId })
        .andWhere('order_stats.total_orders > 0');

      if (query.minOrderValue) {
        queryBuilder.andWhere('order_stats.total_spent >= :minOrderValue', { minOrderValue: query.minOrderValue });
      }

      if (query.minOrderCount) {
        queryBuilder.andWhere('order_stats.total_orders >= :minOrderCount', { minOrderCount: query.minOrderCount });
      }

      if (query.lastOrderDays) {
        queryBuilder.andWhere(
          'EXTRACT(EPOCH FROM NOW() - TO_TIMESTAMP(order_stats.last_order_date / 1000)) / 86400 <= :lastOrderDays',
          { lastOrderDays: query.lastOrderDays }
        );
      }

      const results = await queryBuilder
        .orderBy('order_stats.total_spent', 'DESC')
        .addOrderBy('days_since_last_order', 'ASC')
        .limit(query.limit || 20)
        .getRawMany();

      return results.map(result => ({
        customerId: parseInt(result.customer_id),
        customerName: result.customer_name || 'Khách hàng',
        email: this.extractPrimaryEmail(result.email),
        phone: result.phone,
        avatarUrl: result.avatar_url,
        totalOrders: parseInt(result.total_orders || '0'),
        totalSpent: parseFloat(result.total_spent || '0'),
        averageOrderValue: parseFloat(result.average_order_value || '0'),
        lastOrderDate: result.last_order_date ? this.formatDate(result.last_order_date) : null,
        daysSinceLastOrder: Math.floor(parseFloat(result.days_since_last_order || '0')),
        potentialScore: this.calculatePotentialScore(result),
        tags: [] // TODO: Implement customer tags
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy dữ liệu khách hàng tiềm năng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Utility methods
   */
  private extractPrimaryEmail(emailJson: any): string {
    if (!emailJson) return '';
    if (typeof emailJson === 'string') return emailJson;
    if (typeof emailJson === 'object') {
      return emailJson.primary || emailJson.email || Object.values(emailJson)[0] || '';
    }
    return '';
  }

  private calculatePotentialScore(customerData: any): number {
    const totalSpent = parseFloat(customerData.total_spent || '0');
    const totalOrders = parseInt(customerData.total_orders || '0');
    const daysSinceLastOrder = parseFloat(customerData.days_since_last_order || '0');

    // Simple scoring algorithm (can be improved)
    let score = 0;

    // Score based on total spent (0-40 points)
    if (totalSpent > 5000000) score += 40;
    else if (totalSpent > 2000000) score += 30;
    else if (totalSpent > 1000000) score += 20;
    else if (totalSpent > 500000) score += 10;

    // Score based on order frequency (0-30 points)
    if (totalOrders > 10) score += 30;
    else if (totalOrders > 5) score += 20;
    else if (totalOrders > 2) score += 10;

    // Score based on recency (0-30 points)
    if (daysSinceLastOrder <= 7) score += 30;
    else if (daysSinceLastOrder <= 30) score += 20;
    else if (daysSinceLastOrder <= 90) score += 10;

    return Math.min(score, 100);
  }

  private formatDate(timestamp: number | Date | string): string | null {
    try {
      let date: Date;

      if (timestamp instanceof Date) {
        date = timestamp;
      } else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
      } else if (typeof timestamp === 'number') {
        date = new Date(timestamp);
      } else {
        throw new Error('Invalid timestamp format');
      }

      // Check if date is valid
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date value');
      }

      return date.toISOString().split('T')[0];
    } catch (error) {
      this.logger.warn(`Lỗi khi format date: ${error.message}, timestamp: ${timestamp}`);
      return null;
    }
  }
}
