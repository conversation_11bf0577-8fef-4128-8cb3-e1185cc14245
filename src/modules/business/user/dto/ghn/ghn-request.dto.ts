import { IsString, IsOptional, IsN<PERSON>ber, IsArray, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { GHNProductDto, GHNOrderDto } from './ghn-config.dto';

/**
 * DTO cho request tạo đơn hàng GHN
 */
export class CreateGHNOrderRequestDto extends GHNOrderDto {}

/**
 * DTO cho request cập nhật đơn hàng GHN
 */
export class UpdateGHNOrderRequestDto {
  @ApiProperty({
    description: 'Mã đơn hàng GHN',
    example: 'FFFNL9HH'
  })
  @IsString()
  orderCode: string;

  @ApiProperty({
    description: 'Tên người gửi',
    example: 'Nguyễn Văn A',
    required: false
  })
  @IsOptional()
  @IsString()
  fromName?: string;

  @ApiProperty({
    description: '<PERSON><PERSON> điện thoại người gửi',
    example: '0912345678',
    required: false
  })
  @IsOptional()
  @IsString()
  fromPhone?: string;

  @ApiProperty({
    description: 'Địa chỉ người gửi',
    example: '123 Đường ABC',
    required: false
  })
  @IsOptional()
  @IsString()
  fromAddress?: string;

  @ApiProperty({
    description: 'Mã phường/xã người gửi',
    example: '20107',
    required: false
  })
  @IsOptional()
  @IsString()
  fromWardCode?: string;

  @ApiProperty({
    description: 'ID quận/huyện người gửi',
    example: 1442,
    required: false
  })
  @IsOptional()
  @IsNumber()
  fromDistrictId?: number;

  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Trần Thị B',
    required: false
  })
  @IsOptional()
  @IsString()
  toName?: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0987654321',
    required: false
  })
  @IsOptional()
  @IsString()
  toPhone?: string;

  @ApiProperty({
    description: 'Địa chỉ người nhận',
    example: '456 Đường DEF',
    required: false
  })
  @IsOptional()
  @IsString()
  toAddress?: string;

  @ApiProperty({
    description: 'Mã phường/xã người nhận',
    example: '20201',
    required: false
  })
  @IsOptional()
  @IsString()
  toWardCode?: string;

  @ApiProperty({
    description: 'ID quận/huyện người nhận',
    example: 1443,
    required: false
  })
  @IsOptional()
  @IsNumber()
  toDistrictId?: number;

  @ApiProperty({
    description: 'Số tiền thu hộ (VND)',
    example: 500000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  codAmount?: number;

  @ApiProperty({
    description: 'Nội dung đơn hàng',
    example: 'Laptop Gaming',
    required: false
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: 'Khối lượng (gram)',
    example: 2000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  weight?: number;

  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 30,
    required: false
  })
  @IsOptional()
  @IsNumber()
  length?: number;

  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 20,
    required: false
  })
  @IsOptional()
  @IsNumber()
  width?: number;

  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 5,
    required: false
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({
    description: 'Ghi chú',
    example: 'Gọi trước khi giao',
    required: false
  })
  @IsOptional()
  @IsString()
  note?: string;
}

/**
 * DTO cho request tính phí vận chuyển GHN
 */
export class CalculateGHNFeeRequestDto {
  @ApiProperty({
    description: 'ID cửa hàng',
    example: 123456,
    required: false
  })
  @IsOptional()
  @IsNumber()
  shopId?: number;

  @ApiProperty({
    description: 'ID dịch vụ',
    example: 53320,
    required: false
  })
  @IsOptional()
  @IsNumber()
  serviceId?: number;

  @ApiProperty({
    description: 'Loại dịch vụ (2: E-commerce, 5: Truyền thống)',
    example: 2,
    required: false
  })
  @IsOptional()
  @IsNumber()
  serviceTypeId?: number;

  @ApiProperty({
    description: 'Giá trị bảo hiểm (VND)',
    example: 1000000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  insuranceValue?: number;

  @ApiProperty({
    description: 'Mã giảm giá',
    example: 'DISCOUNT10',
    required: false
  })
  @IsOptional()
  @IsString()
  coupon?: string;

  @ApiProperty({
    description: 'ID quận/huyện gửi',
    example: 1442,
    required: false
  })
  @IsOptional()
  @IsNumber()
  fromDistrictId?: number;

  @ApiProperty({
    description: 'Mã phường/xã gửi',
    example: '20107',
    required: false
  })
  @IsOptional()
  @IsString()
  fromWardCode?: string;

  @ApiProperty({
    description: 'ID quận/huyện nhận',
    example: 1443
  })
  @IsNumber()
  toDistrictId: number;

  @ApiProperty({
    description: 'Mã phường/xã nhận',
    example: '20201'
  })
  @IsString()
  toWardCode: string;

  @ApiProperty({
    description: 'Khối lượng (gram)',
    example: 2000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  weight?: number;

  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 30,
    required: false
  })
  @IsOptional()
  @IsNumber()
  length?: number;

  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 20,
    required: false
  })
  @IsOptional()
  @IsNumber()
  width?: number;

  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 5,
    required: false
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({
    description: 'Số tiền thu hộ (VND)',
    example: 500000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  codValue?: number;
}

/**
 * DTO cho request hủy đơn hàng GHN
 */
export class CancelGHNOrderRequestDto {
  @ApiProperty({
    description: 'Danh sách mã đơn hàng cần hủy',
    example: ['FFFNL9HH', 'GGGML8II']
  })
  @IsArray()
  @IsString({ each: true })
  orderCodes: string[];
}

/**
 * DTO cho request in nhãn đơn hàng GHN
 */
export class PrintGHNOrderRequestDto {
  @ApiProperty({
    description: 'Danh sách mã đơn hàng cần in',
    example: ['FFFNL9HH', 'GGGML8II']
  })
  @IsArray()
  @IsString({ each: true })
  orderCodes: string[];
}

/**
 * DTO cho request cập nhật COD GHN
 */
export class UpdateGHNCODRequestDto {
  @ApiProperty({
    description: 'Mã đơn hàng GHN',
    example: 'FFFNL9HH'
  })
  @IsString()
  orderCode: string;

  @ApiProperty({
    description: 'Số tiền COD mới (VND)',
    example: 600000
  })
  @IsNumber()
  codAmount: number;
}

/**
 * DTO cho request lấy dịch vụ GHN
 */
export class GetGHNServicesRequestDto {
  @ApiProperty({
    description: 'ID cửa hàng',
    example: 123456
  })
  @IsNumber()
  shopId: number;

  @ApiProperty({
    description: 'ID quận/huyện gửi',
    example: 1442
  })
  @IsNumber()
  fromDistrict: number;

  @ApiProperty({
    description: 'ID quận/huyện nhận',
    example: 1443
  })
  @IsNumber()
  toDistrict: number;
}

/**
 * DTO cho request tính thời gian giao hàng GHN
 */
export class CalculateGHNLeadTimeRequestDto {
  @ApiProperty({
    description: 'ID quận/huyện gửi',
    example: 1442
  })
  @IsNumber()
  fromDistrictId: number;

  @ApiProperty({
    description: 'Mã phường/xã gửi',
    example: '20107',
    required: false
  })
  @IsOptional()
  @IsString()
  fromWardCode?: string;

  @ApiProperty({
    description: 'ID quận/huyện nhận',
    example: 1443
  })
  @IsNumber()
  toDistrictId: number;

  @ApiProperty({
    description: 'Mã phường/xã nhận',
    example: '20201'
  })
  @IsString()
  toWardCode: string;

  @ApiProperty({
    description: 'ID dịch vụ',
    example: 53320
  })
  @IsNumber()
  serviceId: number;
}

/**
 * DTO cho request tạo cửa hàng GHN
 */
export class CreateGHNStoreRequestDto {
  @ApiProperty({
    description: 'ID quận/huyện',
    example: 1442
  })
  @IsNumber()
  districtId: number;

  @ApiProperty({
    description: 'Mã phường/xã',
    example: '20107'
  })
  @IsString()
  wardCode: string;

  @ApiProperty({
    description: 'Tên cửa hàng',
    example: 'Cửa hàng ABC'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Số điện thoại',
    example: '0912345678'
  })
  @IsString()
  phone: string;

  @ApiProperty({
    description: 'Địa chỉ chi tiết',
    example: '123 Đường ABC, Quận 1'
  })
  @IsString()
  address: string;
}

/**
 * DTO cho request lấy bưu cục GHN
 */
export class GetGHNStationsRequestDto {
  @ApiProperty({
    description: 'ID quận/huyện',
    example: 1442
  })
  @IsNumber()
  districtId: number;

  @ApiProperty({
    description: 'Mã phường/xã',
    example: '20107',
    required: false
  })
  @IsOptional()
  @IsString()
  wardCode?: string;

  @ApiProperty({
    description: 'Offset',
    example: 0,
    required: false
  })
  @IsOptional()
  @IsNumber()
  offset?: number;

  @ApiProperty({
    description: 'Limit',
    example: 10,
    required: false
  })
  @IsOptional()
  @IsNumber()
  limit?: number;
}

/**
 * DTO cho request tạo ticket GHN
 */
export class CreateGHNTicketRequestDto {
  @ApiProperty({
    description: 'Mã đơn hàng liên quan',
    example: 'FFFNL9HH'
  })
  @IsString()
  orderCode: string;

  @ApiProperty({
    description: 'Chủ đề yêu cầu',
    example: 'Tư vấn',
    enum: ['Tư vấn', 'Hối Giao/Lấy/Trả hàng', 'Thay đổi thông tin', 'Khiếu nại']
  })
  @IsString()
  category: string;

  @ApiProperty({
    description: 'Nội dung yêu cầu',
    example: 'Cần hỗ trợ về thời gian giao hàng'
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Email khách hàng',
    example: '<EMAIL>',
    required: false
  })
  @IsOptional()
  @IsString()
  cEmail?: string;
}

/**
 * DTO cho request phản hồi ticket GHN
 */
export class ReplyGHNTicketRequestDto {
  @ApiProperty({
    description: 'ID ticket',
    example: 1605822
  })
  @IsNumber()
  ticketId: number;

  @ApiProperty({
    description: 'Nội dung phản hồi',
    example: 'Cảm ơn bạn đã hỗ trợ'
  })
  @IsString()
  description: string;
}

/**
 * DTO cho webhook data từ GHN
 */
export class GHNWebhookDataDto {
  @ApiProperty({
    description: 'Loại webhook',
    example: 'switch_status'
  })
  Type: string;

  @ApiProperty({
    description: 'Mã đơn hàng GHN',
    example: 'FFFNL9HH'
  })
  OrderCode: string;

  @ApiProperty({
    description: 'Shop ID',
    example: 123456
  })
  ShopID: number;

  @ApiProperty({
    description: 'Mã đơn hàng khách hàng',
    example: 'ORDER_123456',
    required: false
  })
  @IsOptional()
  ClientOrderCode?: string;

  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    example: 'delivered'
  })
  Status: string;

  @ApiProperty({
    description: 'Mô tả trạng thái',
    example: 'Đã giao hàng thành công'
  })
  Description: string;

  @ApiProperty({
    description: 'Khối lượng (gram)',
    example: 2000
  })
  Weight: number;

  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 30
  })
  Length: number;

  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 20
  })
  Width: number;

  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 5
  })
  Height: number;

  @ApiProperty({
    description: 'Số tiền COD (VND)',
    example: 500000
  })
  CODAmount: number;

  @ApiProperty({
    description: 'Chi tiết phí',
    example: {
      MainService: 30000,
      Insurance: 5000,
      Return: 0,
      R2S: 0,
      StationDO: 0,
      StationPU: 0,
      CODFailedFee: 0,
      CODFee: 0,
      Coupon: 0
    }
  })
  Fee: {
    MainService: number;
    Insurance: number;
    Return: number;
    R2S: number;
    StationDO: number;
    StationPU: number;
    CODFailedFee: number;
    CODFee: number;
    Coupon: number;
  };

  @ApiProperty({
    description: 'Tổng phí (VND)',
    example: 35000
  })
  TotalFee: number;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: '2023-12-01T10:30:00Z'
  })
  Time: string;
}
