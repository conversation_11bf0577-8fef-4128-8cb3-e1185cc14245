import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response chung của GHN
 */
export class GHNBaseResponseDto {
  @ApiProperty({
    description: 'Mã trạng thái',
    example: 200
  })
  @Expose()
  code: number;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Success'
  })
  @Expose()
  message: string;
}

/**
 * DTO cho thông tin đơn hàng đã tạo GHN
 */
export class GHNCreatedOrderDto {
  @ApiProperty({
    description: 'Mã đơn hàng GHN',
    example: 'FFFNL9HH'
  })
  @Expose()
  orderCode: string;

  @ApiProperty({
    description: 'Tổng phí vận chuyển',
    example: 35000
  })
  @Expose()
  totalFee: number;

  @ApiProperty({
    description: 'Chi tiết phí',
    example: {
      mainService: 30000,
      insurance: 5000,
      coupon: 0,
      stationDo: 0,
      stationPu: 0,
      return: 0,
      r2s: 0
    }
  })
  @Expose()
  fee: {
    mainService: number;
    insurance: number;
    coupon: number;
    stationDo: number;
    stationPu: number;
    return: number;
    r2s: number;
  };

  @ApiProperty({
    description: 'Thời gian giao hàng dự kiến',
    example: '2023-12-03T16:00:00Z'
  })
  @Expose()
  expectedDeliveryTime: string;
}

/**
 * DTO cho response tạo đơn hàng GHN
 */
export class CreateGHNOrderResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin đơn hàng đã tạo',
    type: GHNCreatedOrderDto
  })
  @Expose()
  data: GHNCreatedOrderDto;
}

/**
 * DTO cho response tính phí GHN
 */
export class CalculateGHNFeeResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin phí vận chuyển',
    example: {
      total: 35000,
      serviceFee: 30000,
      insuranceFee: 5000,
      pickStationFee: 0,
      couponValue: 0,
      r2sFee: 0,
      documentReturn: 0,
      doubleCheck: 0,
      codFee: 0,
      pickRemoteAreasFee: 0,
      deliverRemoteAreasFee: 0,
      codFailedFee: 0
    }
  })
  @Expose()
  data: {
    total: number;
    serviceFee: number;
    insuranceFee: number;
    pickStationFee: number;
    couponValue: number;
    r2sFee: number;
    documentReturn: number;
    doubleCheck: number;
    codFee: number;
    pickRemoteAreasFee: number;
    deliverRemoteAreasFee: number;
    codFailedFee: number;
  };
}

/**
 * DTO cho thông tin đơn hàng GHN
 */
export class GHNOrderInfoDto {
  @ApiProperty({
    description: 'Mã đơn hàng GHN',
    example: 'FFFNL9HH'
  })
  @Expose()
  orderCode: string;

  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    example: 'delivered'
  })
  @Expose()
  status: string;

  @ApiProperty({
    description: 'Tên người gửi',
    example: 'Cửa hàng ABC'
  })
  @Expose()
  fromName: string;

  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Nguyễn Văn A'
  })
  @Expose()
  toName: string;

  @ApiProperty({
    description: 'Khối lượng (gram)',
    example: 2000
  })
  @Expose()
  weight: number;

  @ApiProperty({
    description: 'Số tiền COD',
    example: 500000
  })
  @Expose()
  codAmount: number;

  @ApiProperty({
    description: 'Phí vận chuyển',
    example: 35000
  })
  @Expose()
  fee: number;

  @ApiProperty({
    description: 'Ngày tạo đơn',
    example: '2023-12-01T10:00:00Z'
  })
  @Expose()
  createdDate: string;

  @ApiProperty({
    description: 'Ngày cập nhật',
    example: '2023-12-01T15:30:00Z'
  })
  @Expose()
  updatedDate: string;
}

/**
 * DTO cho response thông tin đơn hàng GHN
 */
export class GetGHNOrderInfoResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin đơn hàng',
    type: [GHNOrderInfoDto]
  })
  @Expose()
  data: GHNOrderInfoDto[];
}

/**
 * DTO cho kết quả hủy đơn hàng GHN
 */
export class GHNCancelOrderResultDto {
  @ApiProperty({
    description: 'Mã đơn hàng',
    example: 'FFFNL9HH'
  })
  @Expose()
  orderCode: string;

  @ApiProperty({
    description: 'Kết quả hủy',
    example: true
  })
  @Expose()
  result: boolean;

  @ApiProperty({
    description: 'Thông báo',
    example: 'OK'
  })
  @Expose()
  message: string;
}

/**
 * DTO cho response hủy đơn hàng GHN
 */
export class CancelGHNOrderResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Kết quả hủy đơn hàng',
    type: [GHNCancelOrderResultDto]
  })
  @Expose()
  data: GHNCancelOrderResultDto[];
}

/**
 * DTO cho thông tin cửa hàng GHN
 */
export class GHNStoreDto {
  @ApiProperty({
    description: 'ID cửa hàng',
    example: 123456
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên cửa hàng',
    example: 'Cửa hàng ABC'
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Số điện thoại',
    example: '0912345678'
  })
  @Expose()
  phone: string;

  @ApiProperty({
    description: 'Địa chỉ',
    example: '123 Đường ABC, Quận 1, TP.HCM'
  })
  @Expose()
  address: string;

  @ApiProperty({
    description: 'Mã phường/xã',
    example: '20107'
  })
  @Expose()
  wardCode: string;

  @ApiProperty({
    description: 'ID quận/huyện',
    example: 1442
  })
  @Expose()
  districtId: number;

  @ApiProperty({
    description: 'Client ID',
    example: 789012
  })
  @Expose()
  clientId: number;

  @ApiProperty({
    description: 'Trạng thái',
    example: 1
  })
  @Expose()
  status: number;
}

/**
 * DTO cho response danh sách cửa hàng GHN
 */
export class GetGHNStoresResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin cửa hàng',
    example: {
      lastOffset: 123456,
      shops: []
    }
  })
  @Expose()
  data: {
    lastOffset: number;
    shops: GHNStoreDto[];
  };
}

/**
 * DTO cho response tạo cửa hàng GHN
 */
export class CreateGHNStoreResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin cửa hàng mới',
    example: { shopId: 123456 }
  })
  @Expose()
  data: {
    shopId: number;
  };
}

/**
 * DTO cho thông tin địa chỉ GHN
 */
export class GHNAddressDto {
  @ApiProperty({
    description: 'ID tỉnh/thành phố',
    example: 202,
    required: false
  })
  @Expose()
  provinceId?: number;

  @ApiProperty({
    description: 'Tên tỉnh/thành phố',
    example: 'Hồ Chí Minh',
    required: false
  })
  @Expose()
  provinceName?: string;

  @ApiProperty({
    description: 'ID quận/huyện',
    example: 1442,
    required: false
  })
  @Expose()
  districtId?: number;

  @ApiProperty({
    description: 'Tên quận/huyện',
    example: 'Quận 1',
    required: false
  })
  @Expose()
  districtName?: string;

  @ApiProperty({
    description: 'Mã phường/xã',
    example: '20107',
    required: false
  })
  @Expose()
  wardCode?: string;

  @ApiProperty({
    description: 'Tên phường/xã',
    example: 'Phường Bến Nghé',
    required: false
  })
  @Expose()
  wardName?: string;
}

/**
 * DTO cho response địa chỉ GHN
 */
export class GetGHNAddressResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Danh sách địa chỉ',
    type: [GHNAddressDto]
  })
  @Expose()
  data: GHNAddressDto[];
}

/**
 * DTO cho thông tin dịch vụ GHN
 */
export class GHNServiceDto {
  @ApiProperty({
    description: 'ID dịch vụ',
    example: 53320
  })
  @Expose()
  serviceId: number;

  @ApiProperty({
    description: 'Tên ngắn dịch vụ',
    example: 'Chuẩn'
  })
  @Expose()
  shortName: string;

  @ApiProperty({
    description: 'Loại dịch vụ',
    example: 2
  })
  @Expose()
  serviceTypeId: number;
}

/**
 * DTO cho response dịch vụ GHN
 */
export class GetGHNServicesResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Danh sách dịch vụ',
    type: [GHNServiceDto]
  })
  @Expose()
  data: GHNServiceDto[];
}

/**
 * DTO cho thông tin ca lấy hàng GHN
 */
export class GHNPickShiftDto {
  @ApiProperty({
    description: 'ID ca lấy hàng',
    example: 2
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tiêu đề ca lấy hàng',
    example: 'Ca lấy 12-03-2021 (12h00 - 18h00)'
  })
  @Expose()
  title: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu (giây từ 00:00)',
    example: 43200
  })
  @Expose()
  fromTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (giây từ 00:00)',
    example: 64800
  })
  @Expose()
  toTime: number;
}

/**
 * DTO cho response ca lấy hàng GHN
 */
export class GetGHNPickShiftsResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Danh sách ca lấy hàng',
    type: [GHNPickShiftDto]
  })
  @Expose()
  data: GHNPickShiftDto[];
}

/**
 * DTO cho response thời gian giao hàng GHN
 */
export class CalculateGHNLeadTimeResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin thời gian giao hàng',
    example: {
      leadtime: **********,
      orderDate: **********
    }
  })
  @Expose()
  data: {
    leadtime: number;
    orderDate: number;
  };
}

/**
 * DTO cho thông tin bưu cục GHN
 */
export class GHNStationDto {
  @ApiProperty({
    description: 'ID bưu cục',
    example: 1888
  })
  @Expose()
  locationId: number;

  @ApiProperty({
    description: 'Tên bưu cục',
    example: 'Bưu cục GHN Quận 1'
  })
  @Expose()
  locationName: string;

  @ApiProperty({
    description: 'Địa chỉ bưu cục',
    example: '123 Đường ABC, Quận 1, TP.HCM'
  })
  @Expose()
  address: string;

  @ApiProperty({
    description: 'Vĩ độ',
    example: 10.123
  })
  @Expose()
  latitude: number;

  @ApiProperty({
    description: 'Kinh độ',
    example: 106.123
  })
  @Expose()
  longitude: number;
}

/**
 * DTO cho response bưu cục GHN
 */
export class GetGHNStationsResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Danh sách bưu cục',
    type: [GHNStationDto]
  })
  @Expose()
  data: GHNStationDto[];
}

/**
 * DTO cho thông tin ticket GHN
 */
export class GHNTicketDto {
  @ApiProperty({
    description: 'ID ticket',
    example: 1605822
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Mã đơn hàng liên quan',
    example: 'FFFNL9HH'
  })
  @Expose()
  orderCode: string;

  @ApiProperty({
    description: 'Loại ticket',
    example: 'Tư vấn'
  })
  @Expose()
  type: string;

  @ApiProperty({
    description: 'Mô tả',
    example: 'Cần hỗ trợ về thời gian giao hàng'
  })
  @Expose()
  description: string;

  @ApiProperty({
    description: 'Trạng thái',
    example: 'Đang xử lý'
  })
  @Expose()
  status: string;

  @ApiProperty({
    description: 'ID trạng thái',
    example: 1
  })
  @Expose()
  statusId: number;

  @ApiProperty({
    description: 'Ngày tạo',
    example: '2023-12-01T10:00:00Z'
  })
  @Expose()
  createdAt: string;

  @ApiProperty({
    description: 'Ngày cập nhật',
    example: '2023-12-01T15:30:00Z'
  })
  @Expose()
  updatedAt: string;
}

/**
 * DTO cho response tạo ticket GHN
 */
export class CreateGHNTicketResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin ticket đã tạo',
    type: GHNTicketDto
  })
  @Expose()
  data: GHNTicketDto;
}

/**
 * DTO cho response danh sách ticket GHN
 */
export class GetGHNTicketsResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Danh sách ticket',
    type: [GHNTicketDto]
  })
  @Expose()
  data: GHNTicketDto[];
}

/**
 * DTO cho response OTP GHN
 */
export class GetGHNOTPResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin OTP',
    example: { TLL: 600 }
  })
  @Expose()
  data: {
    TLL: number; // Time to live in seconds
  };
}

/**
 * DTO cho response in nhãn GHN
 */
export class PrintGHNOrderResponseDto extends GHNBaseResponseDto {
  @ApiProperty({
    description: 'Token để in nhãn',
    example: 'token_string_here'
  })
  @Expose()
  data: string;
}
