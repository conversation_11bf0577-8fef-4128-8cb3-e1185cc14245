import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response chung của GHTK API
 */
export class GHTKBaseResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true
  })
  @Expose()
  success: boolean;

  @ApiProperty({
    description: 'Thông báo từ API',
    example: 'Thành công!'
  })
  @Expose()
  message: string;

  @ApiProperty({
    description: 'Mã request ID',
    example: 'dd663be1c8d0',
    required: false
  })
  @Expose()
  rid?: string;

  @ApiProperty({
    description: 'Mã trạng thái HTTP',
    example: 200,
    required: false
  })
  @Expose()
  code?: number;
}

/**
 * DTO cho response giải pháp GHTK
 */
export class GHTKSolutionDto {
  @ApiProperty({
    description: 'Mã định danh của giải pháp',
    example: 1340164168838205440
  })
  @Expose()
  solutionId: number;

  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết về giải pháp',
    example: 'Giúp shop đảm bảo ngoại quan sản phẩm ...'
  })
  @Expose()
  description: string;

  @ApiProperty({
    description: 'Tên nhóm giải pháp',
    example: 'Gói giải pháp an toàn hàng hoá toàn diện'
  })
  @Expose()
  groupName: string;
}

/**
 * DTO cho response danh sách giải pháp GHTK
 */
export class GetGHTKSolutionsResponseDto extends GHTKBaseResponseDto {
  @ApiProperty({
    description: 'Danh sách giải pháp',
    type: [GHTKSolutionDto]
  })
  @Expose()
  data: GHTKSolutionDto[];
}

/**
 * DTO cho thông tin đơn hàng đã tạo
 */
export class GHTKCreatedOrderDto {
  @ApiProperty({
    description: 'Mã đơn của đối tác',
    example: '123123a'
  })
  @Expose()
  partnerId: string;

  @ApiProperty({
    description: 'Mã vận đơn do GHTK cấp',
    example: 'S1.A1.2001297581'
  })
  @Expose()
  label: string;

  @ApiProperty({
    description: 'Khu vực',
    example: '1'
  })
  @Expose()
  area: string;

  @ApiProperty({
    description: 'Phí ship',
    example: '30400'
  })
  @Expose()
  fee: string;

  @ApiProperty({
    description: 'Phí khai giá',
    example: '15000'
  })
  @Expose()
  insuranceFee: string;

  @ApiProperty({
    description: 'Mã số tracking của GHTK',
    example: 2001297581
  })
  @Expose()
  trackingId: number;

  @ApiProperty({
    description: 'Thời gian dự kiến lấy hàng',
    example: 'Sáng 2017-07-01'
  })
  @Expose()
  estimatedPickTime: string;

  @ApiProperty({
    description: 'Thời gian dự kiến giao hàng',
    example: 'Chiều 2017-07-01'
  })
  @Expose()
  estimatedDeliverTime: string;

  @ApiProperty({
    description: 'Danh sách sản phẩm',
    example: []
  })
  @Expose()
  products: any[];

  @ApiProperty({
    description: 'Mã trạng thái đơn',
    example: 2
  })
  @Expose()
  statusId: number;
}

/**
 * DTO cho response tạo đơn hàng GHTK
 */
export class CreateGHTKOrderResponseDto extends GHTKBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin đơn hàng đã tạo',
    type: GHTKCreatedOrderDto
  })
  @Expose()
  order: GHTKCreatedOrderDto;
}

/**
 * DTO cho phụ phí
 */
export class GHTKExtFeeDto {
  @ApiProperty({
    description: 'Hiển thị phụ phí',
    example: '(+ 7,400 đ)'
  })
  @Expose()
  display: string;

  @ApiProperty({
    description: 'Tên phụ phí',
    example: 'Phụ phí hàng dễ vỡ'
  })
  @Expose()
  title: string;

  @ApiProperty({
    description: 'Số tiền phụ phí',
    example: 7400
  })
  @Expose()
  amount: number;

  @ApiProperty({
    description: 'Loại phụ phí',
    example: 'fragile'
  })
  @Expose()
  type: string;
}

/**
 * DTO cho thông tin phí vận chuyển
 */
export class GHTKFeeDto {
  @ApiProperty({
    description: 'Tên gói cước',
    example: 'area1'
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Cước vận chuyển (VNĐ)',
    example: 30400
  })
  @Expose()
  fee: number;

  @ApiProperty({
    description: 'Phí khai giá (VNĐ)',
    example: 15000
  })
  @Expose()
  insuranceFee: number;

  @ApiProperty({
    description: 'Loại giao hàng',
    example: 'only_hanoi'
  })
  @Expose()
  deliveryType: string;

  @ApiProperty({
    description: 'Khu vực',
    example: 3
  })
  @Expose()
  a: number;

  @ApiProperty({
    description: 'Loại địa phương',
    example: 'local'
  })
  @Expose()
  dt: string;

  @ApiProperty({
    description: 'Danh sách phụ phí',
    type: [GHTKExtFeeDto]
  })
  @Expose()
  extFees: GHTKExtFeeDto[];

  @ApiProperty({
    description: 'Khả năng giao tới khu vực này',
    example: true
  })
  @Expose()
  delivery: boolean;
}

/**
 * DTO cho response tính phí vận chuyển GHTK
 */
export class CalculateGHTKFeeResponseDto extends GHTKBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin phí vận chuyển',
    type: GHTKFeeDto
  })
  @Expose()
  fee: GHTKFeeDto;
}

/**
 * DTO cho thông tin trạng thái đơn hàng
 */
export class GHTKOrderStatusDto {
  @ApiProperty({
    description: 'Mã vận đơn GHTK',
    example: 'S1.A1.17373471'
  })
  @Expose()
  labelId: string;

  @ApiProperty({
    description: 'Mã đơn đối tác',
    example: '1234567'
  })
  @Expose()
  partnerId: string;

  @ApiProperty({
    description: 'Mã trạng thái đơn',
    example: '1'
  })
  @Expose()
  status: string;

  @ApiProperty({
    description: 'Mô tả trạng thái',
    example: 'Chưa tiếp nhận'
  })
  @Expose()
  statusText: string;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2016-10-31 22:32:08'
  })
  @Expose()
  created: string;

  @ApiProperty({
    description: 'Thời gian cập nhật gần nhất',
    example: '2016-10-31 22:32:08'
  })
  @Expose()
  modified: string;

  @ApiProperty({
    description: 'Ghi chú/tình trạng cụ thể',
    example: 'Không giao hàng 1 phần'
  })
  @Expose()
  message: string;

  @ApiProperty({
    description: 'Ngày hẹn lấy hàng',
    example: '2017-09-13'
  })
  @Expose()
  pickDate: string;

  @ApiProperty({
    description: 'Ngày hẹn giao hàng',
    example: '2017-09-14'
  })
  @Expose()
  deliverDate: string;

  @ApiProperty({
    description: 'Họ tên người nhận',
    example: 'Vân Nguyễn'
  })
  @Expose()
  customerFullname: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0911222333'
  })
  @Expose()
  customerTel: string;

  @ApiProperty({
    description: 'Địa chỉ nhận hàng',
    example: '123 Nguyễn Chí Thanh, Quận 1, TP Hồ Chí Minh'
  })
  @Expose()
  address: string;

  @ApiProperty({
    description: 'Số ngày lưu kho',
    example: 0
  })
  @Expose()
  storageDay: number;

  @ApiProperty({
    description: 'Phí ship',
    example: 50000
  })
  @Expose()
  shipMoney: number;

  @ApiProperty({
    description: 'Phí khai giá',
    example: 15000
  })
  @Expose()
  insurance: number;

  @ApiProperty({
    description: 'Giá trị đã khai báo',
    example: 2000000
  })
  @Expose()
  value: number;

  @ApiProperty({
    description: 'Khối lượng đơn (gram)',
    example: 500
  })
  @Expose()
  weight: number;

  @ApiProperty({
    description: 'Số tiền COD',
    example: 30000
  })
  @Expose()
  pickMoney: number;

  @ApiProperty({
    description: 'Thông tin freeship',
    example: 0
  })
  @Expose()
  isFreeship: number;

  @ApiProperty({
    description: 'Mã trạng thái',
    example: 1
  })
  @Expose()
  statusId: number;
}

/**
 * DTO cho response lấy trạng thái đơn hàng GHTK
 */
export class GetGHTKOrderStatusResponseDto extends GHTKBaseResponseDto {
  @ApiProperty({
    description: 'Thông tin trạng thái đơn hàng',
    type: GHTKOrderStatusDto
  })
  @Expose()
  order: GHTKOrderStatusDto;
}

/**
 * DTO cho response hủy đơn hàng GHTK
 */
export class CancelGHTKOrderResponseDto extends GHTKBaseResponseDto {
  @ApiProperty({
    description: 'Log ID để đối chiếu',
    example: 'log_12345'
  })
  @Expose()
  logId: string;
}

/**
 * DTO cho thông tin địa chỉ lấy hàng
 */
export class GHTKPickupAddressDto {
  @ApiProperty({
    description: 'Mã địa chỉ',
    example: '88256'
  })
  @Expose()
  pickAddressId: string;

  @ApiProperty({
    description: 'Địa chỉ chi tiết',
    example: 'Số nhà 105, ngõ 13 Lĩnh Nam, Mai Động, Hoàng Mai, Hà Nội'
  })
  @Expose()
  address: string;

  @ApiProperty({
    description: 'Số điện thoại liên hệ tại kho',
    example: '0987654321'
  })
  @Expose()
  pickTel: string;

  @ApiProperty({
    description: 'Tên kho hoặc tên người phụ trách kho',
    example: 'Store 1'
  })
  @Expose()
  pickName: string;
}

/**
 * DTO cho response danh sách địa chỉ lấy hàng GHTK
 */
export class GetGHTKPickupAddressesResponseDto extends GHTKBaseResponseDto {
  @ApiProperty({
    description: 'Danh sách địa chỉ lấy hàng',
    type: [GHTKPickupAddressDto]
  })
  @Expose()
  data: GHTKPickupAddressDto[];
}

/**
 * DTO cho response danh sách địa chỉ cấp 4 GHTK
 */
export class GetGHTKLevel4AddressResponseDto extends GHTKBaseResponseDto {
  @ApiProperty({
    description: 'Danh sách địa chỉ cấp 4',
    example: [
      'IIG - 75 Giang Văn Minh',
      'Vinapaco Building - 142 Đội Cấn',
      'THCS Thống Nhất',
      'Ngõ 47 Đội Cấn',
      'Ngõ 46 Đội Cấn'
    ]
  })
  @Expose()
  data: string[];
}

/**
 * DTO cho thông tin sản phẩm GHTK
 */
export class GHTKProductInfoDto {
  @ApiProperty({
    description: 'Tên đầy đủ sản phẩm',
    example: 'Laptop Asus'
  })
  @Expose()
  fullName: string;

  @ApiProperty({
    description: 'Mã sản phẩm của GHTK',
    example: '23304A3MHLMVMXX625'
  })
  @Expose()
  productCode: string;

  @ApiProperty({
    description: 'Khối lượng tham chiếu (kg)',
    example: 2
  })
  @Expose()
  weigh: number;

  @ApiProperty({
    description: 'Giá trị (VNĐ) đã khai báo',
    example: 8000000
  })
  @Expose()
  cost: number;
}

/**
 * DTO cho response tìm kiếm sản phẩm GHTK
 */
export class SearchGHTKProductResponseDto extends GHTKBaseResponseDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm tìm được',
    type: [GHTKProductInfoDto]
  })
  @Expose()
  data: GHTKProductInfoDto[];
}

/**
 * DTO cho webhook data từ GHTK
 */
export class GHTKWebhookDataDto {
  @ApiProperty({
    description: 'Mã đơn bên đối tác',
    example: '1234567'
  })
  @Expose()
  partnerId: string;

  @ApiProperty({
    description: 'Mã vận đơn GHTK',
    example: 'S1.A1.17373471'
  })
  @Expose()
  labelId: string;

  @ApiProperty({
    description: 'Mã trạng thái mới của đơn',
    example: 5
  })
  @Expose()
  statusId: number;

  @ApiProperty({
    description: 'Thời gian cập nhật trạng thái',
    example: '2016-11-02T12:18:39+07:00'
  })
  @Expose()
  actionTime: string;

  @ApiProperty({
    description: 'Mã lý do',
    example: ''
  })
  @Expose()
  reasonCode: string;

  @ApiProperty({
    description: 'Mô tả chi tiết lý do thay đổi trạng thái',
    example: ''
  })
  @Expose()
  reason: string;

  @ApiProperty({
    description: 'Trọng lượng đơn hàng (kg)',
    example: 2.4
  })
  @Expose()
  weight: number;

  @ApiProperty({
    description: 'Phí ship đã áp dụng (VNĐ)',
    example: 15000
  })
  @Expose()
  fee: number;

  @ApiProperty({
    description: 'Số tiền thu hộ (VNĐ)',
    example: 100000
  })
  @Expose()
  pickMoney: number;

  @ApiProperty({
    description: 'Đơn giao hàng một phần (1: có, 0: không)',
    example: 0
  })
  @Expose()
  returnPartPackage: number;
}
