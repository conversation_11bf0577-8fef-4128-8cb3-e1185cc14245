import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { IGHNConfig } from '@modules/business/interfaces/ghn.interface';
import {
  GHN_BASE_URLS,
  GHN_TEST_CONFIG,
  GHN_TIMEOUT,
  GHN_ERROR_MESSAGES
} from '@modules/business/constants/ghn.constants';

/**
 * Helper để validate và xây dựng cấu hình GHN
 */
@Injectable()
export class GHNConfigValidationHelper {
  private readonly logger = new Logger(GHNConfigValidationHelper.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Validate và xây dựng cấu hình GHN từ environment variables
   */
  validateAndBuildConfig(): IGHNConfig {
    try {
      this.logger.log('Bắt đầu validate cấu hình <PERSON>N');

      // Lấy các giá trị từ environment variables
      const token = this.configService.get<string>('GHN_TOKEN');
      const shopId = this.configService.get<string>('GHN_SHOP_ID');
      const baseUrl = this.configService.get<string>('GHN_BASE_URL');
      const timeout = this.configService.get<number>('GHN_TIMEOUT');
      const isTestMode = this.configService.get<boolean>('GHN_TEST_MODE');

      // Xây dựng cấu hình với fallback values
      const config: IGHNConfig = {
        token: token || GHN_TEST_CONFIG.TOKEN,
        shopId: shopId || GHN_TEST_CONFIG.SHOP_ID,
        baseUrl: baseUrl || this.getBaseUrl(isTestMode),
        timeout: timeout || GHN_TIMEOUT,
        isTestMode: isTestMode !== undefined ? isTestMode : true
      };

      // Validate cấu hình
      this.validateConfig(config);

      this.logger.log('Cấu hình GHN hợp lệ', {
        baseUrl: config.baseUrl,
        isTestMode: config.isTestMode,
        hasToken: !!config.token,
        hasShopId: !!config.shopId
      });

      return config;
    } catch (error) {
      this.logger.error('Lỗi khi validate cấu hình GHN:', error);
      throw error;
    }
  }

  /**
   * Validate cấu hình GHN
   */
  private validateConfig(config: IGHNConfig): void {
    const errors: string[] = [];

    // Validate token
    if (!config.token || config.token.trim() === '') {
      errors.push('Token GHN không được để trống');
    }

    // Validate shop ID
    if (!config.shopId || config.shopId.trim() === '') {
      errors.push('Shop ID GHN không được để trống');
    }

    // Validate base URL
    if (!config.baseUrl || !this.isValidUrl(config.baseUrl)) {
      errors.push('Base URL GHN không hợp lệ');
    }

    // Validate timeout
    if (config.timeout && (config.timeout < 1000 || config.timeout > 300000)) {
      errors.push('Timeout phải từ 1000ms đến 300000ms');
    }

    if (errors.length > 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHN_INVALID_CONFIG,
        `Cấu hình GHN không hợp lệ: ${errors.join(', ')}`
      );
    }
  }

  /**
   * Lấy base URL dựa trên môi trường
   */
  private getBaseUrl(isTestMode?: boolean): string {
    return isTestMode !== false ? GHN_BASE_URLS.TEST : GHN_BASE_URLS.PRODUCTION;
  }

  /**
   * Kiểm tra URL có hợp lệ không
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate token format (có thể mở rộng thêm logic validate)
   */
  validateTokenFormat(token: string): boolean {
    if (!token || token.trim() === '') {
      return false;
    }

    // Có thể thêm logic validate format token cụ thể của GHN
    // Hiện tại chỉ kiểm tra không rỗng
    return token.length > 10; // Token GHN thường dài hơn 10 ký tự
  }

  /**
   * Validate shop ID format
   */
  validateShopIdFormat(shopId: string): boolean {
    if (!shopId || shopId.trim() === '') {
      return false;
    }

    // Shop ID thường là số
    return /^\d+$/.test(shopId);
  }

  /**
   * Tạo cấu hình test cho development
   */
  createTestConfig(): IGHNConfig {
    this.logger.warn('Sử dụng cấu hình test GHN - chỉ dành cho development');

    return {
      token: GHN_TEST_CONFIG.TOKEN,
      shopId: GHN_TEST_CONFIG.SHOP_ID,
      baseUrl: GHN_TEST_CONFIG.BASE_URL,
      timeout: GHN_TIMEOUT,
      isTestMode: true
    };
  }

  /**
   * Log cảnh báo về cấu hình
   */
  logConfigWarnings(config: IGHNConfig): void {
    if (config.token === GHN_TEST_CONFIG.TOKEN) {
      this.logger.warn('Đang sử dụng token test GHN - không dành cho production');
    }

    if (config.shopId === GHN_TEST_CONFIG.SHOP_ID) {
      this.logger.warn('Đang sử dụng shop ID test GHN - không dành cho production');
    }

    if (config.isTestMode) {
      this.logger.warn('GHN đang ở chế độ test');
    }

    if (config.timeout && config.timeout < 10000) {
      this.logger.warn('Timeout GHN thấp có thể gây lỗi kết nối');
    }
  }

  /**
   * Kiểm tra cấu hình có phải production không
   */
  isProductionConfig(config: IGHNConfig): boolean {
    return !config.isTestMode &&
           config.token !== GHN_TEST_CONFIG.TOKEN &&
           config.shopId !== GHN_TEST_CONFIG.SHOP_ID &&
           config.baseUrl === GHN_BASE_URLS.PRODUCTION;
  }

  /**
   * Sanitize cấu hình để log (ẩn thông tin nhạy cảm)
   */
  sanitizeConfigForLogging(config: IGHNConfig): any {
    return {
      baseUrl: config.baseUrl,
      timeout: config.timeout,
      isTestMode: config.isTestMode,
      hasToken: !!config.token,
      tokenLength: config.token?.length || 0,
      hasShopId: !!config.shopId,
      shopIdLength: config.shopId?.length || 0
    };
  }
}
