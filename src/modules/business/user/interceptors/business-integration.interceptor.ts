import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class BusinessIntegrationInterceptor implements NestInterceptor {
  private readonly logger = new Logger(BusinessIntegrationInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    // Chỉ xử lý cho POST /v1/user/business-integration
    if (request.method === 'POST' && request.url === '/v1/user/business-integration') {
      const body = request.body;
      this.logger.log(`Xử lý request body trước khi transform: ${JSON.stringify(body)}`);

      try {
        // Chuyển đổi price từ số nguyên sang đối tượng BusinessPriceDto
        if (body && body.product && typeof body.product.price === 'number') {
          body.product.price = {
            value: body.product.price,
            currency: 'VND' // Mặc định là VND
          };
        }

        // <PERSON><PERSON><PERSON> bảo customFields là một mảng
        if (body && body.customFields && !Array.isArray(body.customFields)) {
          body.customFields = [body.customFields];
        }

        // Đảm bảo mỗi customField có đủ các trường cần thiết
        if (body && body.customFields && Array.isArray(body.customFields)) {
          body.customFields.forEach(field => {
            // Đảm bảo required là boolean
            if (field.required !== undefined && typeof field.required !== 'boolean') {
              if (field.required === 'true' || field.required === '1') {
                field.required = true;
              } else if (field.required === 'false' || field.required === '0') {
                field.required = false;
              } else {
                field.required = Boolean(field.required);
              }
            }

            // Đảm bảo configJson là một đối tượng
            if (field.configJson && typeof field.configJson === 'string') {
              try {
                field.configJson = JSON.parse(field.configJson);
              } catch (e) {
                this.logger.error(`Lỗi khi parse configJson: ${e.message}`);
                // Nếu không parse được, tạo một đối tượng trống
                field.configJson = {};
              }
            } else if (!field.configJson) {
              field.configJson = {};
            }

            // Đảm bảo grid là một đối tượng
            if (field.grid && typeof field.grid === 'string') {
              try {
                field.grid = JSON.parse(field.grid);
              } catch (e) {
                this.logger.error(`Lỗi khi parse grid: ${e.message}`);
                // Nếu không parse được, tạo một đối tượng grid mặc định
                field.grid = { i: 'default', x: 0, y: 0, w: 6, h: 2 };
              }
            } else if (!field.grid) {
              field.grid = { i: 'default', x: 0, y: 0, w: 6, h: 2 };
            }

            // Đảm bảo value là một đối tượng
            if (field.value && typeof field.value === 'string') {
              try {
                field.value = JSON.parse(field.value);
              } catch (e) {
                field.value = { value: field.value };
              }
            } else if (!field.value) {
              field.value = { value: '' };
            }
          });
        }

        this.logger.log(`Request body sau khi transform: ${JSON.stringify(body)}`);
      } catch (error) {
        this.logger.error(`Lỗi khi xử lý request body: ${error.message}`, error.stack);
      }
    }

    return next.handle();
  }
}
