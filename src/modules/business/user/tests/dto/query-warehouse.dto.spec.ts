import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryWarehouseDto } from '../../dto/warehouse/query-warehouse.dto';
import { SortDirection } from '@common/dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

describe('QueryWarehouseDto', () => {
  it('nên xác thực DTO hợp lệ với các giá trị mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.sortBy).toBe('warehouseId');
    expect(dto.sortDirection).toBe(SortDirection.ASC);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      page: 2,
      limit: 20,
      search: 'kho',
      type: WarehouseTypeEnum.PHYSICAL,
      sortBy: 'name',
      sortDirection: SortDirection.DESC,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(2);
    expect(dto.limit).toBe(20);
    expect(dto.search).toBe('kho');
    expect(dto.type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect(dto.sortBy).toBe('name');
    expect(dto.sortDirection).toBe(SortDirection.DESC);
  });

  it('nên thất bại khi page không phải là số nguyên', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      page: 1.5,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const pageErrors = errors.find(e => e.property === 'page');
    expect(pageErrors).toBeDefined();
    expect(pageErrors?.constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi page nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      page: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const pageErrors = errors.find(e => e.property === 'page');
    expect(pageErrors).toBeDefined();
    expect(pageErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi limit không phải là số nguyên', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      limit: 10.5,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const limitErrors = errors.find(e => e.property === 'limit');
    expect(limitErrors).toBeDefined();
    expect(limitErrors?.constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi limit nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      limit: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const limitErrors = errors.find(e => e.property === 'limit');
    expect(limitErrors).toBeDefined();
    expect(limitErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi type không phải là giá trị hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      type: 'INVALID_TYPE',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const typeErrors = errors.find(e => e.property === 'type');
    expect(typeErrors).toBeDefined();
    expect(typeErrors?.constraints).toHaveProperty('isEnum');
  });

  it('nên thất bại khi sortDirection không phải là giá trị hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      sortDirection: 'INVALID',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sortDirectionErrors = errors.find(e => e.property === 'sortDirection');
    expect(sortDirectionErrors).toBeDefined();
    expect(sortDirectionErrors?.constraints).toHaveProperty('isEnum');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      page: '2',
      limit: '20',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.page).toBe('number');
    expect(typeof dto.limit).toBe('number');
    expect(dto.page).toBe(2);
    expect(dto.limit).toBe(20);
  });
});
