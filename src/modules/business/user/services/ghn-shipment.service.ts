import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GHNConfigValidationHelper } from '../helpers/ghn-config-validation.helper';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import {
  GHN_ENDPOINTS,
  GHN_DEFAULT_HEADERS,
  GHN_ERROR_MESSAGES
} from '@modules/business/constants/ghn.constants';
import {
  IGHNConfig,
  IGHNService,
  IGHNCreateOrderResponse,
  IGHNCalculateFeeResponse,
  IGHNOrderInfoResponse,
  IGHNCancelOrderResponse,
  IGHNStoresResponse,
  IGHNCreateStoreResponse,
  IGHNAddressResponse,
  IGHNServicesResponse,
  IGHNPickShiftsResponse,
  IGHNLeadTimeResponse,
  IGHNStationsResponse,
  IGHNTicketResponse,
  IGHNTicketsResponse,
  IGHNOTPResponse,
  IGHNBaseResponse,
  IGHNWebhookData,
  IGHNCreateOrderRequest
} from '@modules/business/interfaces/ghn.interface';
import {
  CreateGHNOrderRequestDto,
  UpdateGHNOrderRequestDto,
  CalculateGHNFeeRequestDto,
  CancelGHNOrderRequestDto,
  PrintGHNOrderRequestDto,
  UpdateGHNCODRequestDto,
  GetGHNServicesRequestDto,
  CalculateGHNLeadTimeRequestDto,
  CreateGHNStoreRequestDto,
  GetGHNStationsRequestDto,
  CreateGHNTicketRequestDto,
  ReplyGHNTicketRequestDto,
  GHNWebhookDataDto
} from '../dto/ghn';

/**
 * Service xử lý tất cả các tính năng vận chuyển GHN
 */
@Injectable()
export class GHNShipmentService implements IGHNService {
  private readonly logger = new Logger(GHNShipmentService.name);
  private config: IGHNConfig;
  private httpClient: AxiosInstance;

  constructor(
    private readonly configService: ConfigService,
    private readonly configValidationHelper: GHNConfigValidationHelper
  ) {
    this.initializeService();
  }

  /**
   * Khởi tạo service
   */
  private initializeService(): void {
    try {
      // Validate và xây dựng cấu hình
      this.config = this.configValidationHelper.validateAndBuildConfig();
      
      // Log cảnh báo nếu cần
      this.configValidationHelper.logConfigWarnings(this.config);

      // Khởi tạo HTTP client
      this.initializeHttpClient();

      this.logger.log('GHN Service initialized', 
        this.configValidationHelper.sanitizeConfigForLogging(this.config)
      );
    } catch (error) {
      this.logger.error('Failed to initialize GHN Service:', error);
      throw error;
    }
  }

  /**
   * Khởi tạo HTTP client với cấu hình
   */
  private initializeHttpClient(): void {
    this.httpClient = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: {
        ...GHN_DEFAULT_HEADERS,
        'Token': this.config.token,
        'ShopId': this.config.shopId
      }
    });

    // Request interceptor
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`GHN Request: ${config.method?.toUpperCase()} ${config.url}`, {
          params: config.params,
          data: config.data
        });
        return config;
      },
      (error) => {
        this.logger.error('GHN Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`GHN Response: ${response.status}`, {
          data: response.data
        });
        return response;
      },
      (error) => {
        this.logger.error('GHN Response Error:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Set cấu hình GHN
   */
  setConfig(config: IGHNConfig): void {
    this.config = config;
    this.initializeHttpClient();
    this.logger.log('GHN config updated');
  }

  /**
   * Lấy cấu hình hiện tại
   */
  getConfig(): IGHNConfig {
    return { ...this.config };
  }

  /**
   * Tạo đơn hàng GHN
   */
  async createOrder(request: CreateGHNOrderRequestDto): Promise<IGHNCreateOrderResponse> {
    try {
      this.logger.log('Tạo đơn hàng GHN', { 
        clientOrderCode: request.clientOrderCode,
        toName: request.toName 
      });

      // Transform DTO to API format
      const apiRequest: IGHNCreateOrderRequest = {
        shop_id: request.shopId,
        to_name: request.toName,
        to_phone: request.toPhone,
        to_address: request.toAddress,
        to_ward_code: request.toWardCode,
        to_district_id: request.toDistrictId,
        from_name: request.fromName,
        from_phone: request.fromPhone,
        from_address: request.fromAddress,
        from_ward_code: request.fromWardCode,
        from_district_id: request.fromDistrictId,
        from_ward_name: request.fromWardName,
        from_district_name: request.fromDistrictName,
        from_province_name: request.fromProvinceName,
        return_phone: request.returnPhone,
        return_address: request.returnAddress,
        return_district_id: request.returnDistrictId,
        return_ward_code: request.returnWardCode,
        client_order_code: request.clientOrderCode,
        cod_amount: request.codAmount,
        content: request.content,
        weight: request.weight,
        length: request.length,
        width: request.width,
        height: request.height,
        pick_station_id: request.pickStationId,
        insurance_value: request.insuranceValue,
        coupon: request.coupon,
        service_type_id: request.serviceTypeId,
        service_id: request.serviceId,
        payment_type_id: request.paymentTypeId,
        note: request.note,
        required_note: request.requiredNote,
        pick_shift: request.pickShift,
        items: request.items?.map(item => ({
          name: item.name,
          code: item.code,
          quantity: item.quantity,
          price: item.price,
          length: item.length,
          width: item.width,
          height: item.height,
          weight: item.weight,
          category: item.category
        }))
      };

      const response: AxiosResponse<IGHNCreateOrderResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.CREATE_ORDER,
        apiRequest
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Tạo đơn hàng GHN thành công', {
        orderCode: response.data.data.order_code,
        totalFee: response.data.data.total_fee
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi tạo đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Cập nhật đơn hàng GHN
   */
  async updateOrder(orderCode: string, request: Partial<UpdateGHNOrderRequestDto>): Promise<IGHNBaseResponse> {
    try {
      this.logger.log('Cập nhật đơn hàng GHN', { orderCode });

      const apiRequest = {
        order_code: orderCode,
        ...request
      };

      const response: AxiosResponse<IGHNBaseResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.UPDATE_ORDER,
        apiRequest
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Cập nhật đơn hàng GHN thành công', { orderCode });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi cập nhật đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Hủy đơn hàng GHN
   */
  async cancelOrder(orderCodes: string[]): Promise<IGHNCancelOrderResponse> {
    try {
      this.logger.log('Hủy đơn hàng GHN', { orderCodes });

      const response: AxiosResponse<IGHNCancelOrderResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.CANCEL_ORDER,
        { order_codes: orderCodes }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Hủy đơn hàng GHN thành công', { 
        orderCodes,
        results: response.data.data 
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi hủy đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Trả đơn hàng GHN
   */
  async returnOrder(orderCodes: string[]): Promise<IGHNCancelOrderResponse> {
    try {
      this.logger.log('Trả đơn hàng GHN', { orderCodes });

      const response: AxiosResponse<IGHNCancelOrderResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.RETURN_ORDER,
        { order_codes: orderCodes }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Trả đơn hàng GHN thành công', { orderCodes });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi trả đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Giao lại đơn hàng GHN
   */
  async deliveryAgain(orderCodes: string[]): Promise<IGHNCancelOrderResponse> {
    try {
      this.logger.log('Giao lại đơn hàng GHN', { orderCodes });

      const response: AxiosResponse<IGHNCancelOrderResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.DELIVERY_AGAIN,
        { order_codes: orderCodes }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Giao lại đơn hàng GHN thành công', { orderCodes });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi giao lại đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy thông tin đơn hàng GHN
   */
  async getOrderInfo(orderCode: string): Promise<IGHNOrderInfoResponse> {
    try {
      this.logger.log('Lấy thông tin đơn hàng GHN', { orderCode });

      const response: AxiosResponse<IGHNOrderInfoResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.ORDER_INFO,
        { order_code: orderCode }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy thông tin đơn hàng GHN thành công', { orderCode });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy thông tin đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy thông tin đơn hàng theo mã khách hàng
   */
  async getOrderInfoByClientCode(clientOrderCode: string): Promise<IGHNOrderInfoResponse> {
    try {
      this.logger.log('Lấy thông tin đơn hàng GHN theo mã KH', { clientOrderCode });

      const response: AxiosResponse<IGHNOrderInfoResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.ORDER_INFO_BY_CLIENT_CODE,
        { client_order_code: clientOrderCode }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy thông tin đơn hàng GHN theo mã KH thành công', { clientOrderCode });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy thông tin đơn hàng GHN theo mã KH:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Xem trước đơn hàng GHN
   */
  async previewOrder(request: Omit<CreateGHNOrderRequestDto, 'shopId'>): Promise<IGHNCreateOrderResponse> {
    try {
      this.logger.log('Xem trước đơn hàng GHN');

      const apiRequest = {
        to_name: request.toName,
        to_phone: request.toPhone,
        to_address: request.toAddress,
        to_ward_code: request.toWardCode,
        to_district_id: request.toDistrictId,
        cod_amount: request.codAmount,
        content: request.content,
        weight: request.weight,
        length: request.length,
        width: request.width,
        height: request.height,
        service_type_id: request.serviceTypeId,
        payment_type_id: request.paymentTypeId,
        required_note: request.requiredNote
      };

      const response: AxiosResponse<IGHNCreateOrderResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.PREVIEW_ORDER,
        apiRequest
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Xem trước đơn hàng GHN thành công');
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi xem trước đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Cập nhật COD đơn hàng GHN
   */
  async updateCOD(orderCode: string, codAmount: number): Promise<IGHNBaseResponse> {
    try {
      this.logger.log('Cập nhật COD đơn hàng GHN', { orderCode, codAmount });

      const response: AxiosResponse<IGHNBaseResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.UPDATE_COD,
        { order_code: orderCode, cod_amount: codAmount }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Cập nhật COD đơn hàng GHN thành công', { orderCode, codAmount });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi cập nhật COD đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * In nhãn đơn hàng GHN
   */
  async printOrder(orderCodes: string[]): Promise<IGHNBaseResponse> {
    try {
      this.logger.log('In nhãn đơn hàng GHN', { orderCodes });

      const response: AxiosResponse<IGHNBaseResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.PRINT_ORDER,
        { order_codes: orderCodes }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('In nhãn đơn hàng GHN thành công', { orderCodes });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi in nhãn đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Tính phí vận chuyển GHN
   */
  async calculateFee(params: CalculateGHNFeeRequestDto): Promise<IGHNCalculateFeeResponse> {
    try {
      this.logger.log('Tính phí vận chuyển GHN', {
        fromDistrict: params.fromDistrictId,
        toDistrict: params.toDistrictId,
        weight: params.weight
      });

      const queryParams = {
        shop_id: params.shopId,
        service_id: params.serviceId,
        service_type_id: params.serviceTypeId,
        insurance_value: params.insuranceValue,
        coupon: params.coupon,
        from_district_id: params.fromDistrictId,
        from_ward_code: params.fromWardCode,
        to_district_id: params.toDistrictId,
        to_ward_code: params.toWardCode,
        weight: params.weight,
        length: params.length,
        width: params.width,
        height: params.height,
        cod_value: params.codValue
      };

      const response: AxiosResponse<IGHNCalculateFeeResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.CALCULATE_FEE,
        queryParams
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Tính phí vận chuyển GHN thành công', {
        totalFee: response.data.data.total
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi tính phí vận chuyển GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy phí đơn hàng GHN
   */
  async getOrderFee(orderCode: string): Promise<any> {
    try {
      this.logger.log('Lấy phí đơn hàng GHN', { orderCode });

      const response: AxiosResponse<any> = await this.httpClient.post(
        GHN_ENDPOINTS.ORDER_FEE,
        { order_code: orderCode }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy phí đơn hàng GHN thành công', { orderCode });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy phí đơn hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy danh sách dịch vụ GHN
   */
  async getServices(fromDistrict: number, toDistrict: number): Promise<IGHNServicesResponse> {
    try {
      this.logger.log('Lấy danh sách dịch vụ GHN', { fromDistrict, toDistrict });

      const response: AxiosResponse<IGHNServicesResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.GET_SERVICE,
        {
          shop_id: parseInt(this.config.shopId),
          from_district: fromDistrict,
          to_district: toDistrict
        }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy danh sách dịch vụ GHN thành công', {
        serviceCount: response.data.data.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách dịch vụ GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Tính thời gian giao hàng GHN
   */
  async getLeadTime(params: CalculateGHNLeadTimeRequestDto): Promise<IGHNLeadTimeResponse> {
    try {
      this.logger.log('Tính thời gian giao hàng GHN', params);

      const response: AxiosResponse<IGHNLeadTimeResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.LEADTIME,
        {
          from_district_id: params.fromDistrictId,
          from_ward_code: params.fromWardCode,
          to_district_id: params.toDistrictId,
          to_ward_code: params.toWardCode,
          service_id: params.serviceId
        }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Tính thời gian giao hàng GHN thành công');
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi tính thời gian giao hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy danh sách cửa hàng GHN
   */
  async getStores(params?: any): Promise<IGHNStoresResponse> {
    try {
      this.logger.log('Lấy danh sách cửa hàng GHN');

      const response: AxiosResponse<IGHNStoresResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.GET_STORES,
        params || {}
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy danh sách cửa hàng GHN thành công', {
        storeCount: response.data.data.shops.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách cửa hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Tạo cửa hàng GHN
   */
  async createStore(params: CreateGHNStoreRequestDto): Promise<IGHNCreateStoreResponse> {
    try {
      this.logger.log('Tạo cửa hàng GHN', { name: params.name });

      const response: AxiosResponse<IGHNCreateStoreResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.CREATE_STORE,
        {
          district_id: params.districtId,
          ward_code: params.wardCode,
          name: params.name,
          phone: params.phone,
          address: params.address
        }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Tạo cửa hàng GHN thành công', {
        shopId: response.data.data.shop_id
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi tạo cửa hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy danh sách tỉnh/thành phố GHN
   */
  async getProvinces(): Promise<IGHNAddressResponse> {
    try {
      this.logger.log('Lấy danh sách tỉnh/thành phố GHN');

      const response: AxiosResponse<IGHNAddressResponse> = await this.httpClient.get(
        GHN_ENDPOINTS.GET_PROVINCES
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy danh sách tỉnh/thành phố GHN thành công', {
        provinceCount: response.data.data.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách tỉnh/thành phố GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy danh sách quận/huyện GHN
   */
  async getDistricts(provinceId: number): Promise<IGHNAddressResponse> {
    try {
      this.logger.log('Lấy danh sách quận/huyện GHN', { provinceId });

      const response: AxiosResponse<IGHNAddressResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.GET_DISTRICTS,
        { province_id: provinceId }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy danh sách quận/huyện GHN thành công', {
        districtCount: response.data.data.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách quận/huyện GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy danh sách phường/xã GHN
   */
  async getWards(districtId: number): Promise<IGHNAddressResponse> {
    try {
      this.logger.log('Lấy danh sách phường/xã GHN', { districtId });

      const response: AxiosResponse<IGHNAddressResponse> = await this.httpClient.get(
        `${GHN_ENDPOINTS.GET_WARDS}?district_id=${districtId}`
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy danh sách phường/xã GHN thành công', {
        wardCount: response.data.data.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách phường/xã GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy danh sách bưu cục GHN
   */
  async getStations(params: GetGHNStationsRequestDto): Promise<IGHNStationsResponse> {
    try {
      this.logger.log('Lấy danh sách bưu cục GHN', params);

      const response: AxiosResponse<IGHNStationsResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.GET_STATIONS,
        {
          district_id: params.districtId,
          ward_code: params.wardCode,
          offset: params.offset || 0,
          limit: params.limit || 10
        }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy danh sách bưu cục GHN thành công', {
        stationCount: response.data.data.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách bưu cục GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy danh sách ca lấy hàng GHN
   */
  async getPickShifts(): Promise<IGHNPickShiftsResponse> {
    try {
      this.logger.log('Lấy danh sách ca lấy hàng GHN');

      const response: AxiosResponse<IGHNPickShiftsResponse> = await this.httpClient.get(
        GHN_ENDPOINTS.GET_PICK_SHIFTS
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy danh sách ca lấy hàng GHN thành công', {
        shiftCount: response.data.data.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách ca lấy hàng GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Tạo ticket hỗ trợ GHN
   */
  async createTicket(params: CreateGHNTicketRequestDto): Promise<IGHNTicketResponse> {
    try {
      this.logger.log('Tạo ticket hỗ trợ GHN', { orderCode: params.orderCode });

      const response: AxiosResponse<IGHNTicketResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.CREATE_TICKET,
        {
          order_code: params.orderCode,
          category: params.category,
          description: params.description,
          c_email: params.cEmail
        }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Tạo ticket hỗ trợ GHN thành công', {
        ticketId: response.data.data.id
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi tạo ticket hỗ trợ GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Phản hồi ticket GHN
   */
  async replyTicket(ticketId: number, description: string): Promise<any> {
    try {
      this.logger.log('Phản hồi ticket GHN', { ticketId });

      const response: AxiosResponse<any> = await this.httpClient.post(
        GHN_ENDPOINTS.REPLY_TICKET,
        {
          ticket_id: ticketId,
          description: description
        }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Phản hồi ticket GHN thành công', { ticketId });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi phản hồi ticket GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy danh sách ticket GHN
   */
  async getTickets(): Promise<IGHNTicketsResponse> {
    try {
      this.logger.log('Lấy danh sách ticket GHN');

      const response: AxiosResponse<IGHNTicketsResponse> = await this.httpClient.get(
        GHN_ENDPOINTS.GET_TICKETS
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy danh sách ticket GHN thành công', {
        ticketCount: response.data.data.length
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách ticket GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Lấy OTP cho đối tác GHN
   */
  async getOTP(phone: string): Promise<IGHNOTPResponse> {
    try {
      this.logger.log('Lấy OTP cho đối tác GHN', { phone });

      const response: AxiosResponse<IGHNOTPResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.GET_OTP,
        { phone: phone }
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Lấy OTP cho đối tác GHN thành công', { phone });
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi lấy OTP cho đối tác GHN:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Tạo cửa hàng bằng OTP
   */
  async createStoreByOTP(params: any): Promise<IGHNCreateStoreResponse> {
    try {
      this.logger.log('Tạo cửa hàng GHN bằng OTP', { phone: params.phone });

      const response: AxiosResponse<IGHNCreateStoreResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.CREATE_STORE_BY_OTP,
        params
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Tạo cửa hàng GHN bằng OTP thành công', {
        shopId: response.data.data.shop_id
      });

      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi tạo cửa hàng GHN bằng OTP:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Thêm nhân viên bằng OTP
   */
  async addStaffByOTP(params: any): Promise<IGHNBaseResponse> {
    try {
      this.logger.log('Thêm nhân viên GHN bằng OTP', { phone: params.phone });

      const response: AxiosResponse<IGHNBaseResponse> = await this.httpClient.post(
        GHN_ENDPOINTS.ADD_STAFF_BY_OTP,
        params
      );

      if (response.data.code !== 200) {
        throw new AppException(
          BUSINESS_ERROR_CODES.GHN_API_ERROR,
          response.data.message || GHN_ERROR_MESSAGES.API_ERROR
        );
      }

      this.logger.log('Thêm nhân viên GHN bằng OTP thành công');
      return response.data;
    } catch (error) {
      this.logger.error('Lỗi khi thêm nhân viên GHN bằng OTP:', error);
      this.handleGHNError(error);
    }
  }

  /**
   * Xử lý webhook từ GHN
   */
  async handleWebhook(data: IGHNWebhookData): Promise<void> {
    try {
      this.logger.log('Xử lý webhook GHN', {
        type: data.Type,
        orderCode: data.OrderCode,
        status: data.Status
      });

      // Xử lý logic webhook tùy theo loại
      switch (data.Type) {
        case 'create':
          this.logger.log('Đơn hàng GHN được tạo', { orderCode: data.OrderCode });
          break;
        case 'switch_status':
          this.logger.log('Trạng thái đơn hàng GHN thay đổi', {
            orderCode: data.OrderCode,
            status: data.Status
          });
          break;
        case 'update_weight':
          this.logger.log('Cân nặng đơn hàng GHN được cập nhật', {
            orderCode: data.OrderCode,
            weight: data.Weight
          });
          break;
        case 'update_cod':
          this.logger.log('COD đơn hàng GHN được cập nhật', {
            orderCode: data.OrderCode,
            codAmount: data.CODAmount
          });
          break;
        case 'update_fee':
          this.logger.log('Phí đơn hàng GHN được cập nhật', {
            orderCode: data.OrderCode,
            totalFee: data.TotalFee
          });
          break;
        default:
          this.logger.warn('Loại webhook GHN không xác định', { type: data.Type });
      }

      // Có thể thêm logic xử lý webhook tùy theo yêu cầu business
      // Ví dụ: cập nhật database, gửi notification, etc.

      this.logger.log('Xử lý webhook GHN thành công');
    } catch (error) {
      this.logger.error('Lỗi khi xử lý webhook GHN:', error);
      throw error;
    }
  }

  /**
   * Xử lý lỗi GHN
   */
  private handleGHNError(error: any): never {
    if (error instanceof AppException) {
      throw error;
    }

    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      switch (status) {
        case 401:
          throw new AppException(
            BUSINESS_ERROR_CODES.GHN_INVALID_TOKEN,
            GHN_ERROR_MESSAGES.INVALID_TOKEN
          );
        case 400:
          throw new AppException(
            BUSINESS_ERROR_CODES.GHN_INVALID_CONFIG,
            data?.message || GHN_ERROR_MESSAGES.INVALID_CONFIG
          );
        default:
          throw new AppException(
            BUSINESS_ERROR_CODES.GHN_API_ERROR,
            data?.message || GHN_ERROR_MESSAGES.API_ERROR
          );
      }
    }

    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHN_NETWORK_ERROR,
        GHN_ERROR_MESSAGES.NETWORK_ERROR
      );
    }

    throw new AppException(
      BUSINESS_ERROR_CODES.GHN_API_ERROR,
      error.message || GHN_ERROR_MESSAGES.API_ERROR
    );
  }
}
