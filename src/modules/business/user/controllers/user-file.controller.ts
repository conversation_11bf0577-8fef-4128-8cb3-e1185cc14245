import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiCreatedResponse,
  ApiOkResponse,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { UserFileService } from '@modules/business/user/services';
import {
  CreateFileDto,
  UpdateFileDto,
  FileResponseDto,
  QueryFileDto,
  DeleteFilesDto,
  CreateFilesMultipleDto,
  CreateFilesMultipleResponseDto,
} from '../dto/file';
import { ApiResponseDto } from '@/common/response';
import { SwaggerApiTag } from '@common/swagger';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ErrorCode } from '@common/exceptions';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

/**
 * Controller xử lý các endpoint liên quan đến file cho người dùng
 */
@Controller('user/files')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SwaggerApiTag.USER_WAREHOUSE_VIRTUAL_FILES)
export class UserFileController {
  constructor(private readonly userFileService: UserFileService) {}

  /**
   * Lấy danh sách file với phân trang và lọc
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách file với phân trang và lọc' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách file với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(FileResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FILE_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getFiles(@Query() queryDto: QueryFileDto, @CurrentUser() user: JwtPayload) {
    queryDto.userId = user.id; // Thêm userId vào query để chỉ lấy file của người dùng hiện tại
    const files = await this.userFileService.getFiles(queryDto);
    return ApiResponseDto.success(files, 'Lấy danh sách file thành công');
  }

  /**
   * Lấy danh sách file theo ID thư mục
   */
  @Get('folder/:folderId')
  @ApiOperation({ summary: 'Lấy danh sách file theo ID thư mục' })
  @ApiParam({ name: 'folderId', description: 'ID của thư mục', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách file trong thư mục',
    schema: ApiResponseDto.getSchema([FileResponseDto]),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.FILE_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getFilesByFolderId(
    @Param('folderId', ParseIntPipe) folderId: number,
    @CurrentUser() user: JwtPayload
  ) {
    const files = await this.userFileService.getFilesByFolderId(folderId, user.id);
    return ApiResponseDto.success(files, 'Lấy danh sách file theo thư mục thành công');
  }

  /**
   * Lấy thông tin file theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin file theo ID' })
  @ApiParam({ name: 'id', description: 'ID của file', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết của file',
    schema: ApiResponseDto.getSchema(FileResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FILE_NOT_FOUND,
    BUSINESS_ERROR_CODES.FILE_FETCH_FAILED,
    BUSINESS_ERROR_CODES.FILE_ACCESS_DENIED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getFileById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ) {
    const file = await this.userFileService.getFileById(id, user.id);
    return ApiResponseDto.success(file, 'Lấy thông tin file thành công');
  }

  /**
   * Tạo mới file
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo mới file',
    description: 'Tạo file mới. Nếu không cung cấp folderId, file sẽ được tạo trong thư mục gốc mặc định.'
  })
  @ApiCreatedResponse({
    description: 'Tạo mới file thành công',
    type: () => ApiResponseDto.getSchema(FileResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
    BUSINESS_ERROR_CODES.FILE_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async createFile(
    @Body() createDto: CreateFileDto,
    @CurrentUser() user: JwtPayload
  ) {
    createDto.userId = user.id; // Thêm userId vào DTO
    const file = await this.userFileService.createFile(createDto);
    return ApiResponseDto.created<FileResponseDto>(file, 'Tạo mới file thành công');
  }

  /**
   * Tạo nhiều file cùng lúc
   */
  @Post('multiple')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo nhiều file cùng lúc',
    description: 'Tạo nhiều file mới cùng lúc. Nếu không cung cấp folderId, các file sẽ được tạo trong thư mục gốc mặc định.'
  })
  @ApiCreatedResponse({
    description: 'Tạo nhiều file thành công',
    type: () => ApiResponseDto.getSchema(CreateFilesMultipleResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
    BUSINESS_ERROR_CODES.FILE_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async createMultipleFiles(
    @Body() createDto: CreateFilesMultipleDto,
    @CurrentUser() user: JwtPayload
  ) {
    createDto.userId = user.id; // Thêm userId vào DTO
    const result = await this.userFileService.createMultipleFiles(createDto);
    return ApiResponseDto.created<CreateFilesMultipleResponseDto>(result, `Tạo ${result.successCount} file thành công`);
  }

  /**
   * Cập nhật file
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật file' })
  @ApiParam({ name: 'id', description: 'ID của file', type: 'number' })
  @ApiOkResponse({
    description: 'Cập nhật file thành công',
    schema: ApiResponseDto.getSchema(FileResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FILE_NOT_FOUND,
    BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.FILE_UPDATE_FAILED,
    BUSINESS_ERROR_CODES.FILE_ACCESS_DENIED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateFile(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateFileDto,
    @CurrentUser() user: JwtPayload
  ) {
    const file = await this.userFileService.updateFile(id, updateDto, user.id);
    return ApiResponseDto.success<FileResponseDto>(file, 'Cập nhật file thành công');
  }

  /**
   * Xóa file
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa file' })
  @ApiParam({ name: 'id', description: 'ID của file', type: 'number' })
  @ApiOkResponse({
    description: 'Xóa file thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FILE_NOT_FOUND,
    BUSINESS_ERROR_CODES.FILE_DELETE_FAILED,
    BUSINESS_ERROR_CODES.FILE_ACCESS_DENIED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async deleteFile(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload
  ) {
    await this.userFileService.deleteFile(id, user.id);
    return ApiResponseDto.success(null, 'Xóa file thành công');
  }

  /**
   * Xóa nhiều file
   */
  @Delete('delete')
  @ApiOperation({ summary: 'Xóa nhiều file' })
  @ApiBody({ type: DeleteFilesDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa file thành công.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Đã xóa 2 file thành công.' },
        result: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            deletedCount: { type: 'number', example: 2 },
            failedItems: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  reason: { type: 'string', example: 'Bạn không có quyền xóa file này' },
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.FILE_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @HttpCode(HttpStatus.OK)
  async deleteFiles(@Body() deleteFilesDto: DeleteFilesDto, @CurrentUser() user: JwtPayload) {
    const result = await this.userFileService.deleteFiles(deleteFilesDto.fileIds, user.id);
    return ApiResponseDto.success(result, `Đã xóa ${result.deletedCount} file thành công.`);
  }
}
