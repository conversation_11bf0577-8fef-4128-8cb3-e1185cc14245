/**
 * Interfaces cho GHN API (Giao Hàng Nhanh)
 */

/**
 * Interface cho cấu hình GHN
 */
export interface IGHNConfig {
  token: string;
  shopId: string;
  baseUrl: string;
  timeout?: number;
  isTestMode?: boolean;
}

/**
 * Interface cho sản phẩm GHN
 */
export interface IGHNProduct {
  name: string;
  code?: string;
  quantity: number;
  price?: number;
  length?: number;
  width?: number;
  height?: number;
  weight?: number;
  category?: {
    level1?: string;
    level2?: string;
    level3?: string;
  };
}

/**
 * Interface cho thông tin đơn hàng GHN
 */
export interface IGHNOrder {
  shop_id: number;
  to_name: string;
  to_phone: string;
  to_address: string;
  to_ward_code: string;
  to_district_id: number;
  from_name?: string;
  from_phone?: string;
  from_address?: string;
  from_ward_code?: string;
  from_district_id?: number;
  from_ward_name?: string;
  from_district_name?: string;
  from_province_name?: string;
  return_phone?: string;
  return_address?: string;
  return_district_id?: number;
  return_ward_code?: string;
  client_order_code?: string;
  cod_amount?: number;
  content: string;
  weight: number;
  length: number;
  width: number;
  height: number;
  pick_station_id?: number;
  insurance_value?: number;
  coupon?: string;
  service_type_id: number;
  service_id?: number;
  payment_type_id: number;
  note?: string;
  required_note: string;
  pick_shift?: number;
  items?: IGHNProduct[];
}

/**
 * Interface cho request tạo đơn hàng GHN
 */
export interface IGHNCreateOrderRequest extends IGHNOrder {}

/**
 * Interface cho response chung của GHN
 */
export interface IGHNBaseResponse {
  code: number;
  message: string;
  data?: any;
}

/**
 * Interface cho response tạo đơn hàng GHN
 */
export interface IGHNCreateOrderResponse extends IGHNBaseResponse {
  data: {
    order_code: string;
    total_fee: number;
    fee: {
      main_service: number;
      insurance: number;
      coupon: number;
      station_do: number;
      station_pu: number;
      return: number;
      r2s: number;
    };
    expected_delivery_time: string;
  };
}

/**
 * Interface cho response tính phí GHN
 */
export interface IGHNCalculateFeeResponse extends IGHNBaseResponse {
  data: {
    total: number;
    service_fee: number;
    insurance_fee: number;
    pick_station_fee: number;
    coupon_value: number;
    r2s_fee: number;
    document_return: number;
    double_check: number;
    cod_fee: number;
    pick_remote_areas_fee: number;
    deliver_remote_areas_fee: number;
    cod_failed_fee: number;
  };
}

/**
 * Interface cho response thông tin đơn hàng GHN
 */
export interface IGHNOrderInfoResponse extends IGHNBaseResponse {
  data: Array<{
    order_code: string;
    status: string;
    from_name: string;
    to_name: string;
    weight: number;
    cod_amount: number;
    fee: number;
    created_date: string;
    updated_date: string;
  }>;
}

/**
 * Interface cho response hủy đơn hàng GHN
 */
export interface IGHNCancelOrderResponse extends IGHNBaseResponse {
  data: Array<{
    order_code: string;
    result: boolean;
    message: string;
  }>;
}

/**
 * Interface cho response danh sách cửa hàng GHN
 */
export interface IGHNStoresResponse extends IGHNBaseResponse {
  data: {
    last_offset: number;
    shops: Array<{
      _id: number;
      name: string;
      phone: string;
      address: string;
      ward_code: string;
      district_id: number;
      client_id: number;
      status: number;
    }>;
  };
}

/**
 * Interface cho response tạo cửa hàng GHN
 */
export interface IGHNCreateStoreResponse extends IGHNBaseResponse {
  data: {
    shop_id: number;
  };
}

/**
 * Interface cho response địa chỉ GHN
 */
export interface IGHNAddressResponse extends IGHNBaseResponse {
  data: Array<{
    ProvinceID?: number;
    ProvinceName?: string;
    DistrictID?: number;
    DistrictName?: string;
    ProvinceID?: number;
    WardCode?: string;
    WardName?: string;
    DistrictID?: number;
  }>;
}

/**
 * Interface cho response dịch vụ GHN
 */
export interface IGHNServicesResponse extends IGHNBaseResponse {
  data: Array<{
    service_id: number;
    short_name: string;
    service_type_id: number;
  }>;
}

/**
 * Interface cho response ca lấy hàng GHN
 */
export interface IGHNPickShiftsResponse extends IGHNBaseResponse {
  data: Array<{
    id: number;
    title: string;
    from_time: number;
    to_time: number;
  }>;
}

/**
 * Interface cho response thời gian giao hàng GHN
 */
export interface IGHNLeadTimeResponse extends IGHNBaseResponse {
  data: {
    leadtime: number;
    order_date: number;
  };
}

/**
 * Interface cho response bưu cục GHN
 */
export interface IGHNStationsResponse extends IGHNBaseResponse {
  data: Array<{
    locationId: number;
    locationName: string;
    address: string;
    latitude: number;
    longitude: number;
  }>;
}

/**
 * Interface cho response ticket GHN
 */
export interface IGHNTicketResponse extends IGHNBaseResponse {
  data: {
    id: number;
    order_code: string;
    type: string;
    description: string;
    status: string;
    status_id: number;
    created_at: string;
    updated_at: string;
  };
}

/**
 * Interface cho response danh sách ticket GHN
 */
export interface IGHNTicketsResponse extends IGHNBaseResponse {
  data: Array<{
    id: number;
    order_code: string;
    type: string;
    description: string;
    status: string;
    status_id: number;
    created_at: string;
    updated_at: string;
  }>;
}

/**
 * Interface cho response OTP GHN
 */
export interface IGHNOTPResponse extends IGHNBaseResponse {
  data: {
    TLL: number; // Time to live in seconds
  };
}

/**
 * Interface cho webhook data từ GHN
 */
export interface IGHNWebhookData {
  Type: string;
  OrderCode: string;
  ShopID: number;
  ClientOrderCode?: string;
  Status: string;
  Description: string;
  Weight: number;
  Length: number;
  Width: number;
  Height: number;
  CODAmount: number;
  Fee: {
    MainService: number;
    Insurance: number;
    Return: number;
    R2S: number;
    StationDO: number;
    StationPU: number;
    CODFailedFee: number;
    CODFee: number;
    Coupon: number;
  };
  TotalFee: number;
  Time: string;
}

/**
 * Interface cho GHN Service
 */
export interface IGHNService {
  // Configuration
  setConfig(config: IGHNConfig): void;
  getConfig(): IGHNConfig;

  // Orders
  createOrder(request: IGHNCreateOrderRequest): Promise<IGHNCreateOrderResponse>;
  updateOrder(orderCode: string, request: Partial<IGHNOrder>): Promise<IGHNBaseResponse>;
  cancelOrder(orderCodes: string[]): Promise<IGHNCancelOrderResponse>;
  returnOrder(orderCodes: string[]): Promise<IGHNCancelOrderResponse>;
  deliveryAgain(orderCodes: string[]): Promise<IGHNCancelOrderResponse>;
  getOrderInfo(orderCode: string): Promise<IGHNOrderInfoResponse>;
  getOrderInfoByClientCode(clientOrderCode: string): Promise<IGHNOrderInfoResponse>;
  previewOrder(request: Omit<IGHNOrder, 'shop_id'>): Promise<IGHNCreateOrderResponse>;
  updateCOD(orderCode: string, codAmount: number): Promise<IGHNBaseResponse>;
  printOrder(orderCodes: string[]): Promise<{ data: string }>;

  // Fee Calculation
  calculateFee(params: any): Promise<IGHNCalculateFeeResponse>;
  getOrderFee(orderCode: string): Promise<any>;
  getServices(fromDistrict: number, toDistrict: number): Promise<IGHNServicesResponse>;
  getLeadTime(params: any): Promise<IGHNLeadTimeResponse>;

  // Stores
  getStores(params?: any): Promise<IGHNStoresResponse>;
  createStore(params: any): Promise<IGHNCreateStoreResponse>;

  // Address
  getProvinces(): Promise<IGHNAddressResponse>;
  getDistricts(provinceId: number): Promise<IGHNAddressResponse>;
  getWards(districtId: number): Promise<IGHNAddressResponse>;
  getStations(params: any): Promise<IGHNStationsResponse>;

  // Pick Shifts
  getPickShifts(): Promise<IGHNPickShiftsResponse>;

  // Tickets
  createTicket(params: any): Promise<IGHNTicketResponse>;
  replyTicket(ticketId: number, description: string): Promise<any>;
  getTickets(): Promise<IGHNTicketsResponse>;

  // Affiliate
  getOTP(phone: string): Promise<IGHNOTPResponse>;
  createStoreByOTP(params: any): Promise<IGHNCreateStoreResponse>;
  addStaffByOTP(params: any): Promise<IGHNBaseResponse>;

  // Webhook
  handleWebhook(data: IGHNWebhookData): Promise<void>;
}
