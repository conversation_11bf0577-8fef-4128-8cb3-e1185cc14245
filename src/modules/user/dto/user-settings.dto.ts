import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsObject, IsNumber } from 'class-validator';

/**
 * DTO cho việc cập nhật user settings
 */
export class UpdateUserSettingsDto {
  /**
   * User interface theme stored as JSON
   */
  @ApiPropertyOptional({
    description: 'Cài đặt giao diện người dùng dạng JSON (ví dụ: {"mode": "light", "color": "blue"})',
    example: { mode: 'dark', color: 'blue' },
  })
  @IsOptional()
  @IsObject()
  theme?: Record<string, any>;

  /**
   * User-selected timezone
   */
  @ApiPropertyOptional({
    description: 'Múi giờ được chọn bởi người dùng (ví dụ: UTC, Asia/Ho_Chi_Minh)',
    example: 'Asia/Ho_Chi_Minh',
  })
  @IsOptional()
  @IsString()
  timezone?: string;
}

/**
 * DTO cho response của user settings
 */
export class UserSettingsResponseDto {
  /**
   * ID của bản ghi user settings
   */
  @ApiProperty({
    description: 'ID của bản ghi user settings',
    example: 1,
  })
  @IsNumber()
  id: number;

  /**
   * ID của người dùng
   */
  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  @IsNumber()
  userId: number;

  /**
   * User interface theme stored as JSON
   */
  @ApiProperty({
    description: 'Cài đặt giao diện người dùng dạng JSON',
    example: { mode: 'light' },
  })
  @IsObject()
  theme: Record<string, any>;

  /**
   * User-selected timezone
   */
  @ApiProperty({
    description: 'Múi giờ được chọn bởi người dùng',
    example: 'UTC',
  })
  @IsString()
  timezone: string;

  /**
   * Timestamp when the record was created
   */
  @ApiProperty({
    description: 'Thời điểm tạo bản ghi (timestamp milliseconds)',
    example: 1715523521893,
  })
  @IsNumber()
  createdAt: number;

  /**
   * Timestamp when the record was last updated
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật bản ghi gần nhất (timestamp milliseconds)',
    example: 1715529999000,
  })
  @IsNumber()
  updatedAt: number;
}
