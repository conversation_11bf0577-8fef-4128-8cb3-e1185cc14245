import { Body, Controller, Get, Post, Put, UseGuards } from '@nestjs/common';
import { AccountService } from '../service/account.service';
import { UpdateBankInfoDto } from '../../dto/update-bank-info.dto';
import { UpdateBusinessInfoDto } from '../../dto/update-business-info.dto';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { BankInfoResponse, BusinessInfoResponse } from '../../dto/account.dto';
import {
  AvatarUploadDto,
  AvatarUploadResponseDto,
  UpdateAvatarDto,
} from '../../dto/avatar-upload.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { UpdateNotificationSettingsDto, UserNotificationResponseDto } from '../../dto/notification.dto';

@ApiTags(SWAGGER_API_TAGS.USER_ACCOUNT)
@Controller('user/account')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AccountController {
  constructor(private readonly accountService: AccountService) {}

  @ApiOperation({ summary: 'Cập nhật thông tin tài khoản ngân hàng' })
  @ApiBody({
    type: UpdateBankInfoDto,
    description: 'Thông tin tài khoản ngân hàng cần cập nhật',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thông tin tài khoản ngân hàng thành công',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa đăng nhập',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy người dùng',
  })
  /**
   * Cập nhật thông tin tài khoản ngân hàng
   * @param req Request chứa thông tin người dùng
   * @param updateBankInfoDto Thông tin tài khoản ngân hàng cần cập nhật
   * @returns Thông tin tài khoản ngân hàng đã cập nhật
   */
  @Put('bank-info')
  @ApiResponse({
    type: ApiResponseDto,
    description: 'Thông tin tài khoản ngân hàng đã cập nhật',
  })
  async updateBankInfo(
    @CurrentUser() user: JwtPayload,
    @Body() updateBankInfoDto: UpdateBankInfoDto,
  ): Promise<ApiResponseDto<BankInfoResponse>> {
    try {
      // Lấy ID của người dùng từ request
      const userId = user.id;

      // Gọi service để cập nhật thông tin tài khoản ngân hàng
      const updatedUser = await this.accountService.updateBankInfo(
        userId,
        updateBankInfoDto,
      );

      // Trả về kết quả với format ApiResponse
      return ApiResponseDto.success<BankInfoResponse>(
        {
          id: updatedUser.id,
          bankCode: updatedUser.bankCode,
          accountNumber: updatedUser.accountNumber,
          accountHolder: updatedUser.accountHolder,
          bankBranch: updatedUser.bankBranch,
        },
        'Cập nhật thông tin tài khoản ngân hàng thành công',
      );
    } catch (error) {
      // Kiểm tra nếu là AppException thì trả về lỗi tương ứng
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu không phải AppException, trả về lỗi chung
      throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION, error.message);
    }
  }

  @ApiOperation({ summary: 'Lấy thông tin doanh nghiệp của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin doanh nghiệp thành công',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa đăng nhập',
  })
  @Get('business-info')
  async getBusinessInfo(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<BusinessInfoResponse | null>> {
    try {
      // Lấy ID của người dùng từ request
      const userId = user.id;

      // Gọi service để lấy thông tin doanh nghiệp
      const businessInfo = await this.accountService.getBusinessInfo(userId);

      // Trả về kết quả với format ApiResponse
      return ApiResponseDto.success(
        businessInfo
          ? {
              id: businessInfo.id,
              businessName: businessInfo.businessName,
              businessEmail: businessInfo.businessEmail,
              businessPhone: businessInfo.businessPhone,
              businessAddress: businessInfo.businessAddress,
              businessRegistrationCertificate:
                businessInfo.businessRegistrationCertificate,
              taxCode: businessInfo.taxCode,
              representativePosition: businessInfo.representativePosition,
              representativeName: businessInfo.representativeName,
              status: businessInfo.status,
            }
          : null,
        'Lấy thông tin doanh nghiệp thành công',
      );
    } catch (error) {
      // Kiểm tra nếu là AppException thì trả về lỗi tương ứng
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu không phải AppException, trả về lỗi chung
      throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION, error.message);
    }
  }

  /**
   * Cập nhật thông tin doanh nghiệp của người dùng
   * @param updateBusinessInfoDto Thông tin doanh nghiệp cần cập nhật
   * @returns Thông tin doanh nghiệp đã cập nhật
   */
  @Put('business-info')
  @ApiOperation({
    summary: 'Cập nhật thông tin doanh nghiệp của người dùng',
    description:
      'API này cho phép người dùng cập nhật thông tin doanh nghiệp của họ. Nếu chưa có thông tin doanh nghiệp, hệ thống sẽ tạo mới.',
  })
  @ApiBody({
    type: UpdateBusinessInfoDto,
    description: 'Thông tin doanh nghiệp cần cập nhật',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thông tin doanh nghiệp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            success: {
              type: 'boolean',
              example: true,
              description: 'Trạng thái thành công của request',
            },
            data: {
              type: 'object',
              properties: {
                id: {
                  type: 'number',
                  example: 1,
                  description: 'ID của thông tin doanh nghiệp',
                },
                businessName: {
                  type: 'string',
                  example: 'Công ty TNHH ABC',
                  description: 'Tên doanh nghiệp',
                },
                businessEmail: {
                  type: 'string',
                  example: '<EMAIL>',
                  description: 'Email doanh nghiệp',
                },
                businessPhone: {
                  type: 'string',
                  example: '0987654321',
                  description: 'Số điện thoại doanh nghiệp',
                },
                businessAddress: {
                  type: 'string',
                  example: '123 Nguyễn Văn Cừ, Quận 5, Hồ Chí Minh',
                  description: 'Địa chỉ doanh nghiệp',
                },
                businessRegistrationCertificate: {
                  type: 'string',
                  example: 'https://example.com/certificate.pdf',
                  description: 'URL giấy phép kinh doanh',
                },
                taxCode: {
                  type: 'string',
                  example: '0123456789',
                  description: 'Mã số thuế',
                },
                representativeName: {
                  type: 'string',
                  example: 'Nguyễn Văn A',
                  description: 'Tên người đại diện',
                },
                representativePosition: {
                  type: 'string',
                  example: 'Giám đốc',
                  description: 'Vị trí người đại diện',
                },
                status: {
                  type: 'string',
                  example: 'PENDING',
                  description: 'Trạng thái xác thực',
                },
              },
            },
            message: {
              type: 'string',
              example: 'Cập nhật thông tin doanh nghiệp thành công',
              description: 'Thông báo kết quả',
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa đăng nhập',
    schema: {
      properties: {
        success: { type: 'boolean', example: false },
        code: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    schema: {
      properties: {
        success: { type: 'boolean', example: false },
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Dữ liệu không hợp lệ' },
      },
    },
  })
  async updateBusinessInfo(
    @CurrentUser() user: JwtPayload,
    @Body() updateBusinessInfoDto: UpdateBusinessInfoDto,
  ): Promise<ApiResponseDto<BusinessInfoResponse>> {
    try {
      // Lấy ID của người dùng từ request
      const userId = user.id;

      // Gọi service để cập nhật thông tin doanh nghiệp
      const businessInfo = await this.accountService.updateBusinessInfo(
        userId,
        updateBusinessInfoDto,
      );

      // Trả về kết quả với format ApiResponse
      return ApiResponseDto.success<BusinessInfoResponse>(
        {
          id: businessInfo.id,
          businessName: businessInfo.businessName,
          businessEmail: businessInfo.businessEmail,
          businessPhone: businessInfo.businessPhone,
          businessAddress: businessInfo.businessAddress,
          businessRegistrationCertificate:
            businessInfo.businessRegistrationCertificate,
          taxCode: businessInfo.taxCode,
          representativePosition: businessInfo.representativePosition,
          representativeName: businessInfo.representativeName,
          status: businessInfo.status,
        },
        'Cập nhật thông tin doanh nghiệp thành công',
      );
    } catch (error) {
      // Kiểm tra nếu là AppException thì trả về lỗi tương ứng
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu không phải AppException, trả về lỗi chung
      throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION, error.message);
    }
  }

  /**
   * Lấy thông tin tài khoản ngân hàng của người dùng
   * @returns Thông tin tài khoản ngân hàng của người dùng
   */
  @Get('bank-info')
  @ApiOperation({ summary: 'Lấy thông tin tài khoản ngân hàng của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin tài khoản ngân hàng thành công',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa đăng nhập',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy người dùng',
  })
  async getBankInfo(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<BankInfoResponse | null>> {
    try {
      // Lấy ID của người dùng từ request
      const userId = user.id;

      // Gọi service để lấy thông tin tài khoản ngân hàng
      const userBankInfo = await this.accountService.getBankInfo(userId);

      // Kiểm tra xem người dùng có thông tin ngân hàng không
      if (!userBankInfo.bankCode || !userBankInfo.accountNumber) {
        return ApiResponseDto.success(
          null,
          'Người dùng chưa có thông tin tài khoản ngân hàng',
        );
      }

      // Trả về kết quả với format ApiResponse
      return ApiResponseDto.success<BankInfoResponse>(
        {
          id: userBankInfo.id,
          bankCode: userBankInfo.bankCode,
          accountNumber: userBankInfo.accountNumber,
          accountHolder: userBankInfo.accountHolder,
          bankBranch: userBankInfo.bankBranch,
        },
        'Lấy thông tin tài khoản ngân hàng thành công',
      );
    } catch (error) {
      // Kiểm tra nếu là AppException thì trả về lỗi tương ứng
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu không phải AppException, trả về lỗi chung
      throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION, error.message);
    }
  }

  /**
   * Tạo URL tạm thời để tải lên avatar
   * @param req Request chứa thông tin người dùng
   * @param avatarUploadDto Thông tin về loại và kích thước avatar
   * @returns URL tạm thời và thông tin khóa S3
   */
  @Post('avatar/upload-url')
  @ApiResponse({
    status: 200,
    type: AvatarUploadResponseDto,
    description: 'URL tạm thời để tải lên avatar',
  })
  async createAvatarUploadUrl(
    @CurrentUser() user: JwtPayload,
    @Body() avatarUploadDto: AvatarUploadDto,
  ): Promise<ApiResponseDto<AvatarUploadResponseDto>> {
    try {
      // Lấy ID của người dùng từ request
      const userId = user.id;

      // Gọi service để tạo URL tạm thời
      const result = await this.accountService.createAvatarUploadUrl(
        userId,
        avatarUploadDto,
      );

      // Trả về kết quả với format ApiResponse
      return ApiResponseDto.success<AvatarUploadResponseDto>(
        result,
        'Tạo URL tải lên avatar thành công',
      );
    } catch (error) {
      // Xử lý lỗi và trả về response lỗi
      // Kiểm tra nếu là AppException thì trả về lỗi tương ứng
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu không phải AppException, trả về lỗi chung
      throw new AppException(
        ErrorCode.UNCATEGORIZED_EXCEPTION,
        error.message || 'Đã xảy ra lỗi khi tạo URL tải lên avatar',
      );
    }
  }

  /**
   * Cập nhật avatar của người dùng
   * @param req Request chứa thông tin người dùng
   * @param updateAvatarDto Thông tin avatar mới
   * @returns Thông tin người dùng đã cập nhật
   */
  @Put('avatar')
  @ApiResponse({
    status: 200,
    type: ApiResponseDto,
    description: 'Thông tin avatar đã cập nhật',
  })
  async updateAvatar(
    @CurrentUser() user: JwtPayload,
    @Body() updateAvatarDto: UpdateAvatarDto,
  ): Promise<ApiResponseDto<{ avatar: string }>> {
    try {
      // Lấy ID của người dùng từ request
      const userId = user.id;

      // Gọi service để cập nhật avatar
      const updatedUser = await this.accountService.updateAvatar(
        userId,
        updateAvatarDto,
      );

      // Trả về kết quả với format ApiResponse
      return ApiResponseDto.success<{ avatar: string }>(
        { avatar: updatedUser.avatar },
        'Cập nhật avatar thành công',
      );
    } catch (error) {
      // Xử lý lỗi và trả về response lỗi
      // Kiểm tra nếu là AppException thì trả về lỗi tương ứng
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu không phải AppException, trả về lỗi chung
      throw new AppException(
        ErrorCode.UNCATEGORIZED_EXCEPTION,
        error.message || 'Đã xảy ra lỗi khi cập nhật avatar',
      );
    }
  }

  /**
   * Lấy thông tin cài đặt thông báo của user
   * @param user Thông tin user đang đăng nhập
   * @returns Thông tin cài đặt thông báo của user
   */
  @Get('notification-settings')
  @ApiOperation({
    summary: 'Lấy thông tin cài đặt thông báo của user',
    description: 'API này cho phép user xem các cài đặt thông báo của họ',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin cài đặt thông báo thành công',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa đăng nhập',
  })
  async getNotificationSettings(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserNotificationResponseDto>> {
    try {
      // Lấy ID của người dùng từ request
      const userId = user.id;

      // Gọi service để lấy thông tin cài đặt thông báo
      const notificationSettings = await this.accountService.getNotificationSettings(userId);

      // Trả về kết quả với format ApiResponse
      return ApiResponseDto.success(
        notificationSettings,
        'Lấy thông tin cài đặt thông báo thành công',
      );
    } catch (error) {
      // Kiểm tra nếu là AppException thì trả về lỗi tương ứng
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu không phải AppException, trả về lỗi chung
      throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION, error.message);
    }
  }

  /**
   * Cập nhật thông tin cài đặt thông báo của user
   * @param user Thông tin user đang đăng nhập
   * @param updateDto Thông tin cài đặt thông báo cần cập nhật
   * @returns Thông tin cài đặt thông báo đã cập nhật
   */
  @Put('notification-settings')
  @ApiOperation({
    summary: 'Cập nhật thông tin cài đặt thông báo của user',
    description: 'API này cho phép user cập nhật các cài đặt thông báo của họ',
  })
  @ApiBody({
    type: UpdateNotificationSettingsDto,
    description: 'Thông tin cài đặt thông báo cần cập nhật',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thông tin cài đặt thông báo thành công',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Chưa đăng nhập',
  })
  async updateNotificationSettings(
    @CurrentUser() user: JwtPayload,
    @Body() updateDto: UpdateNotificationSettingsDto,
  ): Promise<ApiResponseDto<UserNotificationResponseDto>> {
    try {
      // Lấy ID của người dùng từ request
      const userId = user.id;

      // Gọi service để cập nhật thông tin cài đặt thông báo
      const updatedSettings = await this.accountService.updateNotificationSettings(
        userId,
        updateDto,
      );

      // Trả về kết quả với format ApiResponse
      return ApiResponseDto.success(
        updatedSettings,
        'Cập nhật thông tin cài đặt thông báo thành công',
      );
    } catch (error) {
      // Kiểm tra nếu là AppException thì trả về lỗi tương ứng
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu không phải AppException, trả về lỗi chung
      throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION, error.message);
    }
  }
}
