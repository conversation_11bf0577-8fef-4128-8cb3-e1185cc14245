import { Test, TestingModule } from '@nestjs/testing';
import { UpdateBankInfoDto } from '../../dto/update-bank-info.dto';
import { AccountController } from '../controller/account.controller';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions/app.exception';
import { BusinessInfo } from '../../entities';
import { AccountService } from '../service';

// Mock cho JwtAuthGuard để không cần xác thực thực tế trong test
jest.mock('../../../common/guards/jwt-auth.guard', () => {
  return {
    JwtAuthGuard: jest.fn().mockImplementation(() => ({
      canActivate: jest.fn().mockReturnValue(true), // <PERSON>ôn cho phép truy cập
    })),
  };
});

describe('AccountController', () => {
  let controller: AccountController;
  let accountService: Partial<AccountService>;

  beforeEach(async () => {
    // Tạo mock service
    accountService = {
      updateBankInfo: jest.fn(),
      getBusinessInfo: jest.fn(),
    };

    // Tạo test module
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AccountController],
      providers: [
        {
          provide: AccountService,
          useValue: accountService,
        },
      ],
    })
    .overrideGuard(JwtUserGuard)
    .useValue({ canActivate: () => true })
    .compile();

    controller = module.get<AccountController>(AccountController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('updateBankInfo', () => {
    it('should update bank info and return success response', async () => {
      // Arrange
      // Mock request object with user property
      const req: any = { user: { sub: 1 } };
      const updateBankInfoDto: UpdateBankInfoDto = {
        bankCode: 'VCB',
        accountNumber: '**********',
        accountHolder: 'Nguyen Van A',
        bankBranch: 'Hà Nội',
      };

      const mockUpdatedUser = {
        id: 1,
        bankCode: 'VCB',
        accountNumber: '**********',
        accountHolder: 'Nguyen Van A',
        bankBranch: 'Hà Nội',
      };

      (accountService.updateBankInfo as jest.Mock).mockResolvedValue(mockUpdatedUser);

      // Act
      const result = await controller.updateBankInfo(req, updateBankInfoDto);

      // Assert
      expect(accountService.updateBankInfo).toHaveBeenCalledWith(1, updateBankInfoDto);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.message).toBe('Cập nhật thông tin tài khoản ngân hàng thành công');
      expect(result.result).toEqual({
        id: mockUpdatedUser.id,
        bankCode: mockUpdatedUser.bankCode,
        accountNumber: mockUpdatedUser.accountNumber,
        accountHolder: mockUpdatedUser.accountHolder,
        bankBranch: mockUpdatedUser.bankBranch,
      });
    });
  });

  describe('getBusinessInfo', () => {
    it('should return business info when found', async () => {
      // Arrange
      // Mock request object with user property
      const req: any = { user: { sub: 1 } };
      const mockBusinessInfo: BusinessInfo = {
        id: 1,
        userId: 1,
        businessName: 'Test Company',
        businessEmail: '<EMAIL>',
        businessPhone: '**********',
        businessRegistrationCertificate: 'CERT123',
        taxCode: 'TAX123456',
        representativePosition: 'CEO',
        representativeName: 'Nguyen Van A',
        status: 'active',
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      (accountService.getBusinessInfo as jest.Mock).mockResolvedValue(mockBusinessInfo);

      // Act
      const result = await controller.getBusinessInfo(req);

      // Assert
      expect(accountService.getBusinessInfo).toHaveBeenCalledWith(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.message).toBe('Lấy thông tin doanh nghiệp thành công');
      expect(result.result).toEqual({
        id: mockBusinessInfo.id,
        businessName: mockBusinessInfo.businessName,
        businessEmail: mockBusinessInfo.businessEmail,
        businessPhone: mockBusinessInfo.businessPhone,
        businessRegistrationCertificate: mockBusinessInfo.businessRegistrationCertificate,
        taxCode: mockBusinessInfo.taxCode,
        representativePosition: mockBusinessInfo.representativePosition,
        representativeName: mockBusinessInfo.representativeName,
        status: mockBusinessInfo.status,
      });
    });

    it('should return null when business info not found', async () => {
      // Arrange
      // Mock request object with user property
      const req: any = { user: { sub: 1 } };
      (accountService.getBusinessInfo as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await controller.getBusinessInfo(req);

      // Assert
      expect(accountService.getBusinessInfo).toHaveBeenCalledWith(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.message).toBe('Lấy thông tin doanh nghiệp thành công');
      expect(result.result).toBeNull();
    });

    it('should handle exceptions properly', async () => {
      // Arrange
      // Mock request object with user property
      const req: any = { user: { sub: 1 } };
      const error = new Error('Test error');
      (accountService.getBusinessInfo as jest.Mock).mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getBusinessInfo(req)).rejects.toThrow(AppException);
    });
  });
});