import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';
import { AuthMethodEnum } from '../enums/auth-method.enum';
import { AuthStatusEnum } from '../enums/auth-status.enum';

/**
 * Entity đại diện cho bảng auth_verification_logs trong cơ sở dữ liệu
 * Bảng lưu lịch sử xác thực của người dùng
 */
@Entity('auth_verification_logs')
export class AuthVerificationLog {
  /**
   * ID tự tăng
   */
  @PrimaryGeneratedColumn('increment', { name: 'id' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: true })
  userId: number;

  /**
   * Phương thức xác thực
   */
  @Column({ name: 'auth_method', type: 'enum', enum: AuthMethodEnum, nullable: false })
  authMethod: AuthMethodEnum;

  /**
   * Trạng thái xác thực
   */
  @Column({ name: 'status', type: 'enum', enum: AuthStatusEnum, nullable: false })
  status: AuthStatusEnum;

  /**
   * Địa chỉ IP
   */
  @Column({ name: 'ip_address', length: 45, nullable: true })
  ipAddress: string;

  /**
   * User agent
   */
  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string;

  /**
   * Thời gian gửi mã
   */
  @Column({ name: 'code_sent_at', type: 'bigint', nullable: true })
  codeSentAt: number;

  /**
   * Thời gian xác thực
   */
  @Column({ name: 'verified_at', type: 'bigint', nullable: true })
  verifiedAt: number;

  /**
   * Số lần thử
   */
  @Column({ name: 'attempt_count', default: 0 })
  attemptCount: number;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;
}
