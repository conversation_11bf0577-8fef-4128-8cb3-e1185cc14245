import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { AffiliateAccount } from './affiliate-account.entity';

/**
 * Entity đại diện cho bảng affiliate_customer_order trong cơ sở dữ liệu
 * Đơn hàng affiliate
 */
@Entity('affiliate_customer_order')
export class AffiliateCustomerOrder {
  /**
   * Mã đơn hàng
   */
  @PrimaryColumn({ name: 'order_id', type: 'bigint', comment: 'Mã đơn hàng' })
  orderId: number;

  /**
   * Phần trăm hoa hồng
   */
  @Column({
    name: 'commission',
    type: 'float',
    nullable: true,
    comment: 'Phần trăm hoa hồng'
  })
  commission: number;

  /**
   * ID tài khoản affiliate
   */
  @Column({
    name: 'affiliate_account_id',
    comment: 'tài khoản affiliate'
  })
  affiliateAccountId: number;

  /**
   * Mã rank affiliate không cần nối khóa phụ
   */
  @Column({
    name: 'rank_id',
    type: 'integer',
    nullable: true,
    comment: 'Mã rank affiliate không cần nối khóa phụ'
  })
  rankId: number;
}
