# Hướng dẫn nâng cấp lên XState chính thức

## Giới thiệu

Tài liệu này hướng dẫn cách nâng cấp từ triển khai tùy chỉnh state machine hi<PERSON><PERSON> t<PERSON><PERSON> sang XState chính thức. XState cung cấp nhiều tính năng mạnh mẽ và công cụ trực quan hóa giúp dễ dàng quản lý các quy trình nghiệp vụ phức tạp.

## Cài đặt XState

Đầu tiên, cài đặt phiên bản ổn định của XState:

```bash
npm install xstate@4.38.2 @xstate/fsm@1.6.3
```

## Các bước nâng cấp

### 1. Cập nhật imports

```typescript
// Từ
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AffiliateAccount } from '../entities/affiliate-account.entity';
import { AffiliateContract } from '../entities/affiliate-contract.entity';
import { AffiliateAccountStatus, ContractStatus, ContractType, SignMethod } from '../enums';
import { AppException, ErrorCode } from '@common/exceptions';

// Sang
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Machine, interpret, assign } from 'xstate';
import { AffiliateAccount } from '../entities/affiliate-account.entity';
import { AffiliateContract } from '../entities/affiliate-contract.entity';
import { AffiliateAccountStatus, ContractStatus, ContractType, SignMethod } from '../enums';
import { AppException, ErrorCode } from '@common/exceptions';
```

### 2. Cập nhật định nghĩa máy trạng thái

```typescript
// Từ
private createAffiliateRegistrationMachine() {
  return {
    id: 'affiliateRegistration',
    initial: 'selectAccountType',
    context: {
      userId: 0,
      accountType: 'PERSONAL',
      userData: {},
      otpVerified: false,
    },
    states: {
      // ...
    },
  };
}

// Sang
private createAffiliateRegistrationMachine() {
  return Machine<AffiliateRegistrationContext>({
    id: 'affiliateRegistration',
    initial: 'selectAccountType',
    context: {
      userId: 0,
      accountType: 'PERSONAL',
      userData: {},
      otpVerified: false,
    },
    states: {
      selectAccountType: {
        on: {
          SELECT_PERSONAL: {
            target: 'termsAcceptance',
            actions: assign({
              accountType: (_) => 'PERSONAL',
            }),
          },
          SELECT_BUSINESS: {
            target: 'termsAcceptance',
            actions: assign({
              accountType: (_) => 'BUSINESS',
            }),
          },
        },
      },
      termsAcceptance: {
        on: {
          ACCEPT_TERMS: {
            target: 'personalInfo',
            actions: assign({
              userData: (context, event) => ({
                ...context.userData,
                ...(event.userData || {}),
              }),
            }),
          },
        },
      },
      // Các trạng thái khác...
    },
  });
}
```

### 3. Cập nhật phương thức startRegistration

```typescript
// Từ
async startRegistration(userId: number, initialData: Partial<AffiliateRegistrationContext> = {}) {
  try {
    // ...
    
    // Sử dụng máy trạng thái đã định nghĩa
    const machine = this.createAffiliateRegistrationMachine();
    
    // Tạo một actor giả
    const actor = {
      // ...
    };
    
    // ...
    
    return actor;
  } catch (error) {
    // ...
  }
}

// Sang
async startRegistration(userId: number, initialData: Partial<AffiliateRegistrationContext> = {}) {
  try {
    // ...
    
    // Tạo máy trạng thái với context ban đầu
    const machine = this.createAffiliateRegistrationMachine().withContext({
      ...this.createAffiliateRegistrationMachine().initialState.context,
      userId,
      accountType: initialData.accountType || 'PERSONAL',
      userData: initialData.userData || {},
      otpVerified: false,
      ...(existingContract ? {
        contractId: existingContract.id,
        contractPath: existingContract.documentPath,
      } : {}),
    });
    
    // Khởi tạo service từ máy trạng thái
    const service = interpret(machine)
      .onTransition((state) => {
        this.logger.log(`State changed for user ${userId}: ${JSON.stringify(state.value)}`);
        this.handleStateChange(userId.toString(), state);
      });
    
    // Khởi động service với trạng thái ban đầu
    service.start(initialState);
    
    // Lưu service vào map
    this.machines.set(userId.toString(), service);
    
    return service;
  } catch (error) {
    // ...
  }
}
```

### 4. Cập nhật phương thức sendEvent và getCurrentState

```typescript
// Từ
sendEvent(userId: number, event: string, payload?: any) {
  const userIdStr = userId.toString();
  const actor = this.machines.get(userIdStr);
  
  if (actor) {
    actor.send({ type: event, ...payload });
    return true;
  }
  
  return false;
}

getCurrentState(userId: number) {
  const userIdStr = userId.toString();
  const actor = this.machines.get(userIdStr);
  return actor ? actor.getSnapshot() : null;
}

// Sang
sendEvent(userId: number, event: string, payload?: any) {
  const userIdStr = userId.toString();
  const service = this.machines.get(userIdStr);
  
  if (service) {
    service.send({ type: event, ...payload });
    return true;
  }
  
  return false;
}

getCurrentState(userId: number) {
  const userIdStr = userId.toString();
  const service = this.machines.get(userIdStr);
  return service ? service.state : null;
}
```

### 5. Cập nhật phương thức handleStateChange

```typescript
// Từ
private async handleStateChange(userId: string, state: any) {
  try {
    const userIdNum = parseInt(userId);
    const currentState = typeof state.value === 'string'
      ? state.value
      : Object.keys(state.value)[0];
    const context = state.context;
    
    // ...
  } catch (error) {
    // ...
  }
}

// Sang
private async handleStateChange(userId: string, state: any) {
  try {
    const userIdNum = parseInt(userId);
    const currentState = typeof state.value === 'string'
      ? state.value
      : Object.keys(state.value)[0];
    const context = state.context;
    
    // Phần còn lại giữ nguyên
    // ...
  } catch (error) {
    // ...
  }
}
```

## Lợi ích của việc nâng cấp

1. **Tính năng phong phú hơn**: XState cung cấp nhiều tính năng nâng cao như trạng thái song song, lịch sử trạng thái, v.v.
2. **Công cụ trực quan hóa**: XState Visualizer giúp bạn dễ dàng hiểu và debug máy trạng thái.
3. **Cộng đồng lớn**: XState có cộng đồng lớn và nhiều tài liệu, ví dụ.
4. **Bảo trì tốt hơn**: XState được bảo trì tích cực bởi cộng đồng.

## Trực quan hóa máy trạng thái

XState cung cấp công cụ trực quan hóa tại [XState Visualizer](https://stately.ai/viz). Bạn có thể dán mã máy trạng thái của mình vào đó để xem sơ đồ trực quan.

Ví dụ, để trực quan hóa máy trạng thái đăng ký Affiliate:

```javascript
const affiliateRegistrationMachine = Machine({
  id: 'affiliateRegistration',
  initial: 'selectAccountType',
  context: {
    userId: 0,
    accountType: 'PERSONAL',
    userData: {},
    otpVerified: false,
  },
  states: {
    selectAccountType: {
      on: {
        SELECT_PERSONAL: {
          target: 'termsAcceptance',
          actions: assign({
            accountType: (_) => 'PERSONAL',
          }),
        },
        SELECT_BUSINESS: {
          target: 'termsAcceptance',
          actions: assign({
            accountType: (_) => 'BUSINESS',
          }),
        },
      },
    },
    // Các trạng thái khác...
  },
});
```

## Kết luận

Nâng cấp lên XState chính thức sẽ mang lại nhiều lợi ích cho dự án của bạn, đặc biệt là khi bạn cần quản lý các quy trình nghiệp vụ phức tạp. Tuy nhiên, nếu bạn chỉ cần một giải pháp đơn giản, triển khai tùy chỉnh hiện tại vẫn là một lựa chọn tốt.

Chúng tôi khuyên bạn nên:

1. Cài đặt XState phiên bản ổn định (4.38.2)
2. Nâng cấp từng service một, bắt đầu với các service đơn giản
3. Sử dụng XState Visualizer để kiểm tra máy trạng thái của bạn
4. Tận dụng các tính năng nâng cao của XState khi cần thiết
