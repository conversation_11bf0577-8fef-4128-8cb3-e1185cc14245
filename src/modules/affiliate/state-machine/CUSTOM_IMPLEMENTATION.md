# Triển khai tùy chỉnh State Machine trong RedAI Backend

## Giới thiệu

Tài liệu này mô tả cách chúng tôi đã triển khai một phiên bản tùy chỉnh của state machine cho quy trình đăng ký affiliate trong RedAI Backend. Triển khai này không phụ thuộc vào thư viện XState nhưng vẫn tuân theo các nguyên tắc của máy trạng thái hữu hạn.

## Lý do sử dụng triển khai tùy chỉnh

1. **Tr<PERSON>h phụ thuộc vào thư viện bên ngoài**: <PERSON><PERSON><PERSON><PERSON> thiểu các phụ thuộc bên ngoài có thể giúp ứng dụng nhẹ hơn và dễ bảo trì hơn.
2. **Đơn giản hóa**: Triển khai tùy chỉnh có thể đơn giản hơn và dễ hiểu hơn cho các thành viên mới trong team.
3. **Tùy chỉnh linh hoạt**: <PERSON>úng tôi có thể tùy chỉnh triển khai để phù hợp với nhu cầu cụ thể của dự án.
4. **Tránh các vấn đề về TypeScript**: Tránh các vấn đề về kiểu dữ liệu khi sử dụng XState với TypeScript.

## Cấu trúc triển khai

Triển khai tùy chỉnh của chúng tôi bao gồm ba phần chính:

1. **Định nghĩa máy trạng thái**: Định nghĩa các trạng thái, sự kiện, chuyển đổi và hành động.
2. **Actor**: Quản lý trạng thái hiện tại, xử lý sự kiện và thông báo cho các listeners.
3. **Service**: Quản lý các actors và cung cấp API để tương tác với chúng.

### Định nghĩa máy trạng thái

```typescript
private createAffiliateRegistrationMachine() {
  return {
    id: 'affiliateRegistration',
    initial: 'selectAccountType',
    context: {
      userId: 0,
      accountType: 'PERSONAL',
      userData: {},
      otpVerified: false,
    },
    states: {
      selectAccountType: {
        on: {
          SELECT_PERSONAL: {
            target: 'termsAcceptance',
            actions: [
              (context) => {
                context.accountType = 'PERSONAL';
              },
            ],
          },
          SELECT_BUSINESS: {
            target: 'termsAcceptance',
            actions: [
              (context) => {
                context.accountType = 'BUSINESS';
              },
            ],
          },
        },
      },
      // Các trạng thái khác...
    },
  };
}
```

### Actor

```typescript
const actor = {
  currentState: initialState,
  context: context,
  listeners: [] as Array<(state: any) => void>,
  
  getSnapshot: () => ({
    value: actor.currentState,
    context: actor.context,
  }),
  
  send: (event: any) => {
    // Tìm trạng thái hiện tại trong máy trạng thái
    const currentStateConfig = machine.states[actor.currentState];
    
    // Kiểm tra xem sự kiện có được định nghĩa trong trạng thái hiện tại không
    if (currentStateConfig && currentStateConfig.on && currentStateConfig.on[event.type]) {
      const transition = currentStateConfig.on[event.type];
      
      // Thực hiện các actions nếu có
      if (transition.actions) {
        for (const action of transition.actions) {
          action(actor.context, event);
        }
      }
      
      // Cập nhật trạng thái
      if (transition.target) {
        const nextState = transition.target;
        if (nextState !== actor.currentState) {
          actor.currentState = nextState;
          
          // Thông báo cho tất cả listeners
          actor.listeners.forEach(listener => listener({
            value: actor.currentState,
            context: actor.context,
          }));
        }
      }
    }
  },
  
  subscribe: (listener: (state: any) => void) => {
    actor.listeners.push(listener);
    return {
      unsubscribe: () => {
        const index = actor.listeners.indexOf(listener);
        if (index >= 0) {
          actor.listeners.splice(index, 1);
        }
      },
    };
  },
  
  start: () => {
    // Thông báo trạng thái ban đầu
    actor.listeners.forEach(listener => listener({
      value: actor.currentState,
      context: actor.context,
    }));
  },
};
```

### Service

```typescript
@Injectable()
export class AffiliateRegistrationService {
  private readonly logger = new Logger(AffiliateRegistrationService.name);
  private machines = new Map<string, any>(); // Lưu trữ theo userId

  constructor(
    @InjectRepository(AffiliateAccount)
    private affiliateAccountRepository: Repository<AffiliateAccount>,
    @InjectRepository(AffiliateContract)
    private affiliateContractRepository: Repository<AffiliateContract>,
  ) {}

  async startRegistration(userId: number, initialData: Partial<AffiliateRegistrationContext> = {}) {
    // Khởi tạo actor
    // ...
    return actor;
  }

  sendEvent(userId: number, event: string, payload?: any) {
    const userIdStr = userId.toString();
    const actor = this.machines.get(userIdStr);
    
    if (actor) {
      actor.send({ type: event, ...payload });
      return true;
    }
    
    return false;
  }

  getCurrentState(userId: number) {
    const userIdStr = userId.toString();
    const actor = this.machines.get(userIdStr);
    return actor ? actor.getSnapshot() : null;
  }

  // Các phương thức khác...
}
```

## Cách sử dụng

### 1. Khởi tạo máy trạng thái

```typescript
// Khởi tạo máy trạng thái cho một người dùng
const actor = await affiliateRegistrationService.startRegistration(userId);
```

### 2. Gửi sự kiện

```typescript
// Gửi sự kiện chọn loại tài khoản
affiliateRegistrationService.sendEvent(userId, 'SELECT_PERSONAL');

// Gửi sự kiện chấp nhận điều khoản với dữ liệu
affiliateRegistrationService.sendEvent(userId, 'ACCEPT_TERMS', {
  userData: { termsAccepted: true }
});
```

### 3. Lấy trạng thái hiện tại

```typescript
// Lấy trạng thái hiện tại
const state = affiliateRegistrationService.getCurrentState(userId);
console.log(state.value); // Trạng thái hiện tại, ví dụ: 'personalInfo'
console.log(state.context); // Dữ liệu ngữ cảnh
```

## So sánh với XState

### Ưu điểm

1. **Đơn giản**: Triển khai tùy chỉnh đơn giản hơn và dễ hiểu hơn.
2. **Không có phụ thuộc bên ngoài**: Không cần cài đặt thêm thư viện.
3. **Tùy chỉnh linh hoạt**: Dễ dàng tùy chỉnh để phù hợp với nhu cầu cụ thể.
4. **Ít vấn đề về TypeScript**: Tránh các vấn đề về kiểu dữ liệu phức tạp.

### Nhược điểm

1. **Thiếu tính năng nâng cao**: Không có các tính năng nâng cao như trạng thái song song, lịch sử trạng thái, v.v.
2. **Không có công cụ trực quan hóa**: XState cung cấp công cụ trực quan hóa máy trạng thái.
3. **Phải tự bảo trì**: Phải tự bảo trì code thay vì dựa vào thư viện được cộng đồng bảo trì.

## Kết luận

Triển khai tùy chỉnh state machine là một lựa chọn tốt cho các quy trình đơn giản và khi bạn muốn tránh các phụ thuộc bên ngoài. Tuy nhiên, nếu bạn cần các tính năng nâng cao hoặc quy trình phức tạp hơn, XState vẫn là một lựa chọn tốt hơn.

Chúng tôi khuyên bạn nên:

1. Sử dụng triển khai tùy chỉnh cho các quy trình đơn giản.
2. Sử dụng XState cho các quy trình phức tạp cần các tính năng nâng cao.
3. Đảm bảo tài liệu hóa đầy đủ cách triển khai để các thành viên mới trong team có thể hiểu và sử dụng.
