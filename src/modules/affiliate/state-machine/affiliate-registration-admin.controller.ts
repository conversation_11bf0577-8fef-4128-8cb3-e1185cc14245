import { Body, Controller, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';
import { AffiliateRegistrationService } from './affiliate-registration.service';

/**
 * DTO cho việc từ chối đăng ký
 */
class RejectRegistrationDto {
  reason: string;
}

/**
 * DTO cho phản hồi trạng thái đăng ký
 */
class RegistrationStateDto {
  state: string;
  context: any;
}

@ApiTags('Admin - Affiliate Registration')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/affiliate/registration')
export class AffiliateRegistrationAdminController {
  constructor(private readonly registrationService: AffiliateRegistrationService) {}

  /**
   * Phê duyệt đăng ký affiliate
   */
  @Post(':userId/approve')
  @ApiOperation({ summary: 'Phê duyệt đăng ký affiliate' })
  @ApiParam({
    name: 'userId',
    description: 'ID của người dùng',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Đã phê duyệt đăng ký thành công',
  })
  async approveRegistration(
    @CurrentEmployee() employee: JwtPayload,
    @Param('userId') userId: number,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.registrationService.startRegistration(userId);
    
    // Gửi sự kiện phê duyệt
    this.registrationService.sendEvent(userId, 'ADMIN_APPROVE', {
      employeeId: employee.id,
    });
    
    const currentState = this.registrationService.getCurrentState(userId);
    
    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã phê duyệt đăng ký affiliate thành công',
    );
  }

  /**
   * Từ chối đăng ký affiliate
   */
  @Post(':userId/reject')
  @ApiOperation({ summary: 'Từ chối đăng ký affiliate' })
  @ApiParam({
    name: 'userId',
    description: 'ID của người dùng',
    type: 'number',
  })
  @ApiBody({ type: RejectRegistrationDto })
  @ApiResponse({
    status: 200,
    description: 'Đã từ chối đăng ký thành công',
  })
  async rejectRegistration(
    @CurrentEmployee() employee: JwtPayload,
    @Param('userId') userId: number,
    @Body() dto: RejectRegistrationDto,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.registrationService.startRegistration(userId);
    
    // Gửi sự kiện từ chối
    this.registrationService.sendEvent(userId, 'ADMIN_REJECT', {
      employeeId: employee.id,
      reason: dto.reason,
    });
    
    const currentState = this.registrationService.getCurrentState(userId);
    
    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã từ chối đăng ký affiliate thành công',
    );
  }
}
