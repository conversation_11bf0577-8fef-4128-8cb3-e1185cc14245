import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê lượt click
 */
export class AffiliateClickStatisticsDto {
  /**
   * Tổng số lượt click
   */
  @ApiProperty({
    description: 'Tổng số lượt click',
    example: 1000,
  })
  totalClicks: number;

  /**
   * Số lượt click trong 24 giờ qua
   */
  @ApiProperty({
    description: 'Số lượt click trong 24 giờ qua',
    example: 100,
  })
  clicksLast24Hours: number;

  /**
   * <PERSON><PERSON> lượt click trong 7 ngày qua
   */
  @ApiProperty({
    description: '<PERSON><PERSON> lượt click trong 7 ngày qua',
    example: 500,
  })
  clicksLast7Days: number;

  /**
   * Số lượt click trong 30 ngày qua
   */
  @ApiProperty({
    description: 'Số lượt click trong 30 ngày qua',
    example: 800,
  })
  clicksLast30Days: number;
}
