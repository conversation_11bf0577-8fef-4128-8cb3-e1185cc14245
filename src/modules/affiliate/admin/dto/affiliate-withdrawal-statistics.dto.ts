import { ApiProperty } from '@nestjs/swagger';
import { WithdrawalStatus } from './affiliate-withdrawal-query.dto';

/**
 * DTO cho thống kê tổng quan về yêu cầu rút tiền
 */
export class AffiliateWithdrawalOverviewDto {
  @ApiProperty({
    description: 'Tổng số yêu cầu rút tiền',
    example: 150
  })
  totalWithdrawals: number;

  @ApiProperty({
    description: 'Tổng số tiền đã yêu cầu rút',
    example: 15000000
  })
  totalAmount: number;

  @ApiProperty({
    description: 'Tổng số tiền đã phê duyệt',
    example: 12000000
  })
  totalApprovedAmount: number;

  @ApiProperty({
    description: 'Tổng số tiền đang chờ duyệt',
    example: 2000000
  })
  totalPendingAmount: number;

  @ApiProperty({
    description: 'Tổng số tiền đã từ chối',
    example: 1000000
  })
  totalRejectedAmount: number;

  @ApiProperty({
    description: '<PERSON><PERSON> lượng yêu cầu đang chờ duyệt',
    example: 10
  })
  pendingCount: number;

  @ApiProperty({
    description: 'Số lượng yêu cầu đã phê duyệt',
    example: 120
  })
  approvedCount: number;

  @ApiProperty({
    description: 'Số lượng yêu cầu đã từ chối',
    example: 20
  })
  rejectedCount: number;

  @ApiProperty({
    description: 'Số tiền rút trung bình',
    example: 1000000
  })
  averageWithdrawalAmount: number;
}

/**
 * DTO cho phân bố theo trạng thái
 */
export class WithdrawalStatusDistributionDto {
  @ApiProperty({
    description: 'Trạng thái yêu cầu',
    enum: WithdrawalStatus,
    example: WithdrawalStatus.PENDING
  })
  status: WithdrawalStatus;

  @ApiProperty({
    description: 'Số lượng yêu cầu',
    example: 10
  })
  count: number;

  @ApiProperty({
    description: 'Tổng số tiền',
    example: 10000000
  })
  amount: number;

  @ApiProperty({
    description: 'Phần trăm',
    example: 10.5
  })
  percentage: number;
}

/**
 * DTO cho dữ liệu xu hướng
 */
export class WithdrawalTrendDataDto {
  @ApiProperty({
    description: 'Thời gian (Unix timestamp)',
    example: 1672531200
  })
  time: number;

  @ApiProperty({
    description: 'Số lượng yêu cầu',
    example: 5
  })
  count: number;

  @ApiProperty({
    description: 'Tổng số tiền',
    example: 5000000
  })
  amount: number;
}

/**
 * DTO cho xu hướng yêu cầu rút tiền
 */
export class WithdrawalTrendDto {
  @ApiProperty({
    description: 'Dữ liệu xu hướng',
    type: [WithdrawalTrendDataDto]
  })
  data: WithdrawalTrendDataDto[];

  @ApiProperty({
    description: 'Tổng số lượng yêu cầu',
    example: 150
  })
  totalCount: number;

  @ApiProperty({
    description: 'Tổng số tiền',
    example: 15000000
  })
  totalAmount: number;
}

/**
 * DTO cho thống kê yêu cầu rút tiền
 */
export class AffiliateWithdrawalStatisticsDto {
  @ApiProperty({
    description: 'Thống kê tổng quan về yêu cầu rút tiền',
    type: AffiliateWithdrawalOverviewDto
  })
  overview: AffiliateWithdrawalOverviewDto;

  @ApiProperty({
    description: 'Phân bố theo trạng thái',
    type: [WithdrawalStatusDistributionDto]
  })
  statusDistribution: WithdrawalStatusDistributionDto[];

  @ApiProperty({
    description: 'Xu hướng yêu cầu rút tiền',
    type: WithdrawalTrendDto
  })
  trend: WithdrawalTrendDto;

  @ApiProperty({
    description: 'Thời gian cập nhật thống kê (Unix timestamp)',
    example: 1619171200
  })
  updatedAt: number;
}
