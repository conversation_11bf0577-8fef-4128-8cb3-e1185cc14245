import { Injectable, Logger } from '@nestjs/common';
import {
  AffiliateWithdrawalDto,
  AffiliateWithdrawalOverviewDto,
  AffiliateWithdrawalQueryDto,
  AffiliateWithdrawalStatisticsDto,
  WithdrawalStatus,
} from '../dto';
import { AffiliateWithdrawHistoryRepository } from '@modules/affiliate/repositories/affiliate-withdraw-history.repository';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { AUTH_ERROR_CODE } from '@modules/auth/errors/auth-error.code';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliateWithdrawalService {
  private readonly logger = new Logger(AffiliateWithdrawalService.name);

  constructor(
    private readonly affiliateWithdrawHistoryRepository: AffiliateWithdrawHistoryRepository,
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Lấy danh sách yêu cầu rút tiền với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách yêu cầu rút tiền với phân trang
   */
  @Transactional()
  async getWithdrawals(
    queryDto: AffiliateWithdrawalQueryDto,
  ): Promise<PaginatedResult<AffiliateWithdrawalDto>> {
    try {
      // Lấy danh sách yêu cầu rút tiền với phân trang
      const { items: withdrawals, meta } =
        await this.affiliateWithdrawHistoryRepository.findWithPaginationForAdmin(
          queryDto,
        );

      // Xử lý dữ liệu trả về
      const withdrawalDtos = await Promise.all(
        withdrawals.map(async (withdrawal) => {
          try {
            // Lấy thông tin tài khoản affiliate
            const account = await this.affiliateAccountRepository.findById(
              withdrawal.affiliateAccountId,
            );
            if (!account) {
              this.logger.warn(
                `Affiliate account not found for withdrawal ID: ${withdrawal.id}`,
              );
              return null;
            }

            // Lấy thông tin người dùng
            const user = await this.userRepository.findById(account.userId);
            if (!user) {
              this.logger.warn(`User not found for account ID: ${account.id}`);
              return null;
            }

            return {
              id: withdrawal.id,
              affiliateAccountId: withdrawal.affiliateAccountId,
              user: {
                id: user.id,
                fullName: user.fullName,
                email: user.email,
                phoneNumber: user.phoneNumber,
              },
              amount: withdrawal.amount,
              vatAmount: withdrawal.vatAmount,
              netPayment: withdrawal.netPayment,
              bankInfo: {
                bankCode: withdrawal.bankCode,
                accountNumber: withdrawal.accountNumber,
                accountName: withdrawal.accountName,
              },
              status: withdrawal.status as WithdrawalStatus,
              createdAt: withdrawal.createdAt,
              finishAt: withdrawal.finishAt,
              rejectReason: withdrawal.rejectReason,
              purchaseInvoice: withdrawal.purchaseInvoice,
              processedByEmployeeId: (withdrawal as any).processedByEmployeeId,
              processedByEmployeeName: (withdrawal as any)
                .processedByEmployeeName,
            };
          } catch (error) {
            this.logger.error(
              `Error processing withdrawal ${withdrawal.id}: ${error.message}`,
            );
            return null;
          }
        }),
      );

      // Lọc bỏ các yêu cầu null (do lỗi xử lý)
      const validWithdrawals = withdrawalDtos.filter(
        (withdrawal) => withdrawal !== null,
      ) as AffiliateWithdrawalDto[];

      return {
        items: validWithdrawals,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate withdrawals: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách yêu cầu rút tiền',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết yêu cầu rút tiền
   * @param id ID của yêu cầu rút tiền
   * @returns Thông tin chi tiết yêu cầu rút tiền
   */
  @Transactional()
  async getWithdrawalById(id: number): Promise<AffiliateWithdrawalDto> {
    try {
      // Lấy thông tin yêu cầu rút tiền
      const withdrawal =
        await this.affiliateWithdrawHistoryRepository.findById(id);
      if (!withdrawal) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.WITHDRAWAL_NOT_FOUND,
          'Không tìm thấy yêu cầu rút tiền',
        );
      }

      // Lấy thông tin tài khoản affiliate
      const account = await this.affiliateAccountRepository.findById(
        withdrawal.affiliateAccountId,
      );
      if (!account) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy thông tin người dùng
      const user = await this.userRepository.findById(account.userId);
      if (!user) {
        throw new AppException(
          AUTH_ERROR_CODE.USER_NOT_FOUND,
          'Không tìm thấy người dùng',
        );
      }

      return {
        id: withdrawal.id,
        affiliateAccountId: withdrawal.affiliateAccountId,
        user: {
          id: user.id,
          fullName: user.fullName,
          email: user.email,
          phoneNumber: user.phoneNumber,
        },
        amount: withdrawal.amount,
        vatAmount: withdrawal.vatAmount,
        netPayment: withdrawal.netPayment,
        bankInfo: {
          bankCode: withdrawal.bankCode,
          accountNumber: withdrawal.accountNumber,
          accountName: withdrawal.accountName,
        },
        status: withdrawal.status as WithdrawalStatus,
        createdAt: withdrawal.createdAt,
        finishAt: withdrawal.finishAt,
        rejectReason: withdrawal.rejectReason,
        purchaseInvoice: withdrawal.purchaseInvoice,
        processedByEmployeeId: (withdrawal as any).processedByEmployeeId,
        processedByEmployeeName: (withdrawal as any).processedByEmployeeName,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate withdrawal: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.WITHDRAWAL_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin chi tiết yêu cầu rút tiền',
      );
    }
  }

  /**
   * Phê duyệt yêu cầu rút tiền
   * @param id ID của yêu cầu rút tiền
   * @param employeeId ID của nhân viên phê duyệt
   * @param employeeName Tên của nhân viên phê duyệt
   * @returns Thông tin yêu cầu rút tiền đã phê duyệt
   */
  @Transactional()
  async approveWithdrawal(
    id: number,
    employeeId: number,
  ): Promise<AffiliateWithdrawalDto> {
    try {
      // Lấy thông tin yêu cầu rút tiền
      const withdrawal =
        await this.affiliateWithdrawHistoryRepository.findById(id);
      if (!withdrawal) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.WITHDRAWAL_NOT_FOUND,
          'Không tìm thấy yêu cầu rút tiền',
        );
      }

      // Kiểm tra trạng thái hiện tại
      if (withdrawal.status !== WithdrawalStatus.PENDING) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.WITHDRAWAL_PROCESSING_FAILED,
          'Chỉ có thể phê duyệt yêu cầu đang ở trạng thái chờ duyệt',
        );
      }

      // Cập nhật trạng thái yêu cầu
      await this.affiliateWithdrawHistoryRepository.updateStatus(
        id,
        WithdrawalStatus.APPROVED,
        employeeId,
        Math.floor(Date.now() / 1000),
      );

      // Lấy thông tin yêu cầu đã cập nhật
      return this.getWithdrawalById(id);
    } catch (error) {
      this.logger.error(
        `Error approving affiliate withdrawal: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.WITHDRAWAL_PROCESSING_FAILED,
        'Lỗi khi phê duyệt yêu cầu rút tiền',
      );
    }
  }

  /**
   * Từ chối yêu cầu rút tiền
   * @param id ID của yêu cầu rút tiền
   * @param employeeId ID của nhân viên từ chối
   * @param employeeName Tên của nhân viên từ chối
   * @param rejectReason Lý do từ chối
   * @returns Thông tin yêu cầu rút tiền đã từ chối
   */
  @Transactional()
  async rejectWithdrawal(
    id: number,
    employeeId: number,
    rejectReason: string,
  ): Promise<AffiliateWithdrawalDto> {
    try {
      // Lấy thông tin yêu cầu rút tiền
      const withdrawal =
        await this.affiliateWithdrawHistoryRepository.findById(id);
      if (!withdrawal) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.WITHDRAWAL_NOT_FOUND,
          'Không tìm thấy yêu cầu rút tiền',
        );
      }

      // Kiểm tra trạng thái hiện tại
      if (withdrawal.status !== WithdrawalStatus.PENDING) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.WITHDRAWAL_PROCESSING_FAILED,
          'Chỉ có thể từ chối yêu cầu đang ở trạng thái chờ duyệt',
        );
      }

      // Cập nhật trạng thái yêu cầu
      await this.affiliateWithdrawHistoryRepository.updateStatus(
        id,
        WithdrawalStatus.REJECTED,
        employeeId,
        Math.floor(Date.now() / 1000),
        rejectReason,
      );

      // Hoàn tiền vào tài khoản affiliate
      await this.affiliateAccountRepository.increaseBalance(
        withdrawal.affiliateAccountId,
        withdrawal.amount,
      );

      // Lấy thông tin yêu cầu đã cập nhật
      return this.getWithdrawalById(id);
    } catch (error) {
      this.logger.error(
        `Error rejecting affiliate withdrawal: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.WITHDRAWAL_PROCESSING_FAILED,
        'Lỗi khi từ chối yêu cầu rút tiền',
      );
    }
  }

  /**
   * Lấy thống kê về yêu cầu rút tiền
   * @param begin Thời gian bắt đầu (Unix timestamp)
   * @param end Thời gian kết thúc (Unix timestamp)
   * @param interval Khoảng thời gian cho xu hướng (ngày, tháng, năm)
   * @returns Thống kê về yêu cầu rút tiền
   */
  async getStatistics(
    begin?: number,
    end?: number,
    interval: 'day' | 'month' | 'year' = 'month'
  ): Promise<AffiliateWithdrawalStatisticsDto> {
    try {
      const now = Math.floor(Date.now() / 1000);

      // Nếu không có thời gian bắt đầu, lấy 30 ngày trước
      if (!begin) {
        begin = now - 30 * 24 * 60 * 60;
      }

      // Nếu không có thời gian kết thúc, lấy thời gian hiện tại
      if (!end) {
        end = now;
      }

      // Lấy thống kê tổng quan
      const overviewStats = await this.affiliateWithdrawHistoryRepository.getWithdrawalOverview();

      // Lấy phân bố theo trạng thái
      const statusDistribution = await this.affiliateWithdrawHistoryRepository.getStatusDistribution();

      // Lấy xu hướng theo thời gian
      const trend = await this.affiliateWithdrawHistoryRepository.getTrend(begin, end, interval);

      // Tạo đối tượng thống kê
      const overview: AffiliateWithdrawalOverviewDto = {
        totalWithdrawals: overviewStats.totalWithdrawals,
        totalAmount: overviewStats.totalAmount,
        totalApprovedAmount: overviewStats.totalApprovedAmount,
        totalPendingAmount: overviewStats.totalPendingAmount,
        totalRejectedAmount: overviewStats.totalRejectedAmount,
        pendingCount: overviewStats.pendingCount,
        approvedCount: overviewStats.approvedCount,
        rejectedCount: overviewStats.rejectedCount,
        averageWithdrawalAmount: overviewStats.averageWithdrawalAmount
      };

      // Transform statusDistribution to match the DTO
      const transformedStatusDistribution = statusDistribution.map(item => ({
        status: item.status,
        count: item.count,
        amount: item.totalAmount, // Rename totalAmount to amount
        percentage: item.percentage
      }));

      // Transform trend data to match the DTO
      const transformedTrendData = trend.data.map(item => ({
        time: item.timestamp, // Rename timestamp to time
        count: item.count,
        amount: item.amount
      }));

      return {
        overview,
        statusDistribution: transformedStatusDistribution,
        trend: {
          data: transformedTrendData,
          totalCount: trend.totalCount,
          totalAmount: trend.totalAmount
        },
        updatedAt: now
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate withdrawal statistics: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thống kê về yêu cầu rút tiền',
      );
    }
  }
}
