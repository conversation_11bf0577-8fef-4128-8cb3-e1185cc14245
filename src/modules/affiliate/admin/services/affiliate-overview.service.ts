import { Injectable, Logger } from '@nestjs/common';
import { AffiliateOverviewDto } from '../dto/affiliate-overview.dto';
import { 
  AffiliateAccountRepository,
  AffiliateClickRepository,
  AffiliateCustomerOrderRepository,
  AffiliateRankRepository,
  AffiliatePointConversionHistoryRepository
} from '@modules/affiliate/repositories';
import { AppException } from '@common/exceptions';
import { AffiliateAccountStatus } from '@modules/affiliate/enums/affiliate-account-status.enum';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliateOverviewService {
  private readonly logger = new Logger(AffiliateOverviewService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateClickRepository: AffiliateClickRepository,
    private readonly affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository,
    private readonly affiliateRankRepository: AffiliateRankRepository,
    private readonly affiliatePointConversionHistoryRepository: AffiliatePointConversionHistoryRepository,
  ) {}

  /**
   * Lấy thông tin tổng quan về affiliate
   * @returns Thông tin tổng quan
   */
  @Transactional()
  async getOverview(): Promise<AffiliateOverviewDto> {
    try {
      // Tính tổng số tài khoản publisher (affiliate accounts)
      const totalPublishers = await this.affiliateAccountRepository.countTotal();

      // Tính tổng số cấp bậc affiliate
      const totalRanks = await this.affiliateRankRepository.countTotal();

      // Tính tổng số đơn hàng affiliate
      const totalOrders = await this.affiliateCustomerOrderRepository.countTotal();

      // Tính tổng số lần chuyển đổi điểm
      const totalPointConversions = await this.affiliatePointConversionHistoryRepository.countTotal();

      // Tính tổng số tài khoản
      const totalAccounts = await this.affiliateAccountRepository.countTotal();

      // Tính số tài khoản theo trạng thái
      const activeAccounts =
        await this.affiliateAccountRepository.countByStatus(
          AffiliateAccountStatus.APPROVED,
        );

      // Đếm tài khoản đang chờ xử lý (DRAFT, PENDING_APPROVAL)
      const pendingDraft = await this.affiliateAccountRepository.countByStatus(
        AffiliateAccountStatus.DRAFT,
      );
      const pendingApproval = await this.affiliateAccountRepository.countByStatus(
        AffiliateAccountStatus.PENDING_APPROVAL,
      );
      const pendingAccounts = pendingDraft + pendingApproval;

      // Đếm tài khoản bị chặn (SUSPENDED)
      const blockedAccounts =
        await this.affiliateAccountRepository.countByStatus(
          AffiliateAccountStatus.SUSPENDED,
        );

      // Tính tổng doanh thu và hoa hồng
      const { totalRevenue, totalCommission } =
        await this.affiliateCustomerOrderRepository.calculateTotalRevenueAndCommission();

      // Tính tổng số lượt click
      const totalClicks = await this.affiliateClickRepository.countTotal();

      // Tính tỷ lệ chuyển đổi trung bình
      const averageConversionRate =
        totalClicks > 0 ? (totalOrders / totalClicks) * 100 : 0;

      return {
        totalPublishers,
        totalRanks,
        totalOrders,
        totalPointConversions,
        totalAccounts,
        activeAccounts,
        pendingAccounts,
        blockedAccounts,
        totalRevenue,
        totalCommission,
        totalClicks,
        averageConversionRate,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate overview: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin tổng quan về affiliate',
      );
    }
  }
}
