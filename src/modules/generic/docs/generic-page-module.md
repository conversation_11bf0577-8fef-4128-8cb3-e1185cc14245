# Tài liệu Module Generic Page

## Tổng quan

Module Generic Page là một hệ thống xây dựng trang động cho phép người dùng tạo và quản lý các trang tùy chỉnh dựa trên cấu trúc JSON. Module này cung cấp giao diện quản trị cho admin để tạo, chỉnh sửa và quản lý các trang, cũng như giao diện người dùng để hiển thị các trang đã tạo.

## Tính năng chính

- Tạo và quản lý các trang tùy chỉnh với URL riêng
- Thiết kế giao diện trang bằng trình tạo trang trực quan
- Hỗ trợ nhiều loại component (form, hiển thị)
- Quản lý bố cục (layout) bằng grid system
- Xuất bản/hủy xuất bản trang
- Hỗ trợ mẫu trang (templates)
- Xử lý form và validation
- <PERSON><PERSON><PERSON> hợp API

## Cấu trúc module

```
src/modules/generic-page/
├── admin/                  # Giao diện quản trị
│   ├── components/         # Components cho giao diện admin
│   │   ├── ComponentPalette.tsx
│   │   ├── PageBuilder.tsx
│   │   ├── PagePreview.tsx
│   │   └── ...
│   └── pages/              # Các trang quản trị
│       ├── PageBuilderPage.tsx
│       ├── PageListPage.tsx
│       └── ...
├── components/             # Components cho người dùng
│   ├── GenericPage.tsx     # Component chính để render trang từ JSON
│   ├── ComponentRegistry.tsx # Đăng ký các component có thể sử dụng
│   └── ...
├── hooks/                  # Custom hooks
│   ├── useGenericPage.ts
│   └── ...
├── services/               # Services gọi API
│   ├── genericPage.service.ts
│   └── ...
├── types/                  # Type definitions
│   ├── generic-page.types.ts
│   └── ...
├── utils/                  # Utility functions
│   ├── layoutUtils.ts
│   └── ...
├── routers/                # Cấu hình routing
│   └── genericPageRoutes.tsx
└── index.ts                # Entry point
```

## Luồng làm việc

### Quản trị viên

1. Truy cập trang quản lý trang tại `/admin/generic-pages`
2. Tạo trang mới hoặc chọn trang có sẵn để chỉnh sửa
3. Sử dụng trình tạo trang để thiết kế giao diện
4. Cấu hình các thuộc tính của trang (tên, đường dẫn, mô tả)
5. Xuất bản trang để người dùng có thể truy cập

### Người dùng

1. Truy cập trang tại đường dẫn `/p/{path}` với `{path}` là đường dẫn đã cấu hình
2. Xem nội dung trang và tương tác với các form (nếu có)
3. Gửi dữ liệu form (nếu trang có form)

## Các thành phần chính

### GenericPage

Component chính để hiển thị trang từ cấu trúc JSON. Nó nhận vào cấu hình trang và hiển thị các component tương ứng.

### ComponentRegistry

Đăng ký các component có thể sử dụng trong trang. Mỗi component được đăng ký với một key và một component React.

### PageBuilder

Giao diện trực quan để thiết kế trang. Cho phép kéo thả các component và cấu hình thuộc tính.

### PagePreview

Hiển thị xem trước trang đang được thiết kế.

## Cấu trúc dữ liệu

### GenericPage

```typescript
interface GenericPage {
  id: string;
  name: string;
  description?: string;
  path: string;
  config: GenericFormConfig;
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}
```

### GenericFormConfig

```typescript
interface GenericFormConfig {
  formId: string;
  title: string;
  subtitle?: string;
  groups: GroupConfig[];
  apiConfig?: ApiConfig;
  uiConfig?: UiConfig;
}
```

### GroupConfig

```typescript
interface GroupConfig {
  id: string;
  label: string;
  fields: FieldConfig[];
}
```

### FieldConfig

```typescript
interface FieldConfig {
  component: string;
  config: ComponentConfig;
  grid: GridConfig;
  apiMapping?: ApiMapping;
}
```

## API Backend cần thiết

Dưới đây là danh sách các API mà backend cần cung cấp để đấu nối với frontend:

### 1. Quản lý trang

#### 1.1. Lấy danh sách trang

- **Endpoint**: `GET /v1/generic-pages`
- **Query Parameters**:
  - `page`: Số trang (mặc định: 1)
  - `limit`: Số lượng item trên mỗi trang (mặc định: 10)
  - `search`: Tìm kiếm theo tên, mô tả hoặc đường dẫn
  - `sortBy`: Sắp xếp theo trường (mặc định: createdAt)
  - `sortDirection`: Hướng sắp xếp (ASC, DESC)
  - `status`: Lọc theo trạng thái (draft, published, archived)
- **Response**: `ApiResponseDto<PaginatedResult<GenericPage>>`

#### 1.2. Lấy thông tin trang theo ID

- **Endpoint**: `GET /v1/generic-pages/{id}`
- **Path Parameters**:
  - `id`: ID của trang
- **Response**: `ApiResponseDto<GenericPage>`

#### 1.3. Lấy thông tin trang theo đường dẫn

- **Endpoint**: `GET /v1/generic-pages/by-path/{path}`
- **Path Parameters**:
  - `path`: Đường dẫn của trang
- **Response**: `ApiResponseDto<GenericPage>`

#### 1.4. Tạo trang mới

- **Endpoint**: `POST /v1/generic-pages`
- **Request Body**:
  ```json
  {
    "name": "Tên trang",
    "description": "Mô tả trang",
    "path": "duong-dan-trang",
    "config": {
      // Cấu hình trang (GenericFormConfig)
    }
  }
  ```
- **Response**: `ApiResponseDto<GenericPage>`

#### 1.5. Cập nhật trang

- **Endpoint**: `PUT /v1/generic-pages/{id}`
- **Path Parameters**:
  - `id`: ID của trang
- **Request Body**:
  ```json
  {
    "name": "Tên trang mới",
    "description": "Mô tả trang mới",
    "path": "duong-dan-trang-moi",
    "config": {
      // Cấu hình trang mới (GenericFormConfig)
    }
  }
  ```
- **Response**: `ApiResponseDto<GenericPage>`

#### 1.6. Xóa trang

- **Endpoint**: `DELETE /v1/generic-pages/{id}`
- **Path Parameters**:
  - `id`: ID của trang
- **Response**: `ApiResponseDto<void>`

#### 1.7. Xuất bản trang

- **Endpoint**: `POST /v1/generic-pages/{id}/publish`
- **Path Parameters**:
  - `id`: ID của trang
- **Response**: `ApiResponseDto<GenericPage>`

#### 1.8. Hủy xuất bản trang

- **Endpoint**: `POST /v1/generic-pages/{id}/unpublish`
- **Path Parameters**:
  - `id`: ID của trang
- **Response**: `ApiResponseDto<GenericPage>`

### 2. Quản lý mẫu trang (templates)

#### 2.1. Lấy danh sách mẫu trang

- **Endpoint**: `GET /v1/generic-page-templates`
- **Query Parameters**:
  - `page`: Số trang (mặc định: 1)
  - `limit`: Số lượng item trên mỗi trang (mặc định: 10)
  - `search`: Tìm kiếm theo tên hoặc mô tả
  - `category`: Lọc theo danh mục
  - `tags`: Lọc theo tags
- **Response**: `ApiResponseDto<PaginatedResult<GenericPageTemplate>>`

#### 2.2. Lấy thông tin mẫu trang theo ID

- **Endpoint**: `GET /v1/generic-page-templates/{id}`
- **Path Parameters**:
  - `id`: ID của mẫu trang
- **Response**: `ApiResponseDto<GenericPageTemplate>`

#### 2.3. Tạo mẫu trang mới

- **Endpoint**: `POST /v1/generic-page-templates`
- **Request Body**:
  ```json
  {
    "name": "Tên mẫu trang",
    "description": "Mô tả mẫu trang",
    "category": "Danh mục",
    "tags": ["tag1", "tag2"],
    "config": {
      // Cấu hình mẫu trang (GenericFormConfig)
    }
  }
  ```
- **Response**: `ApiResponseDto<GenericPageTemplate>`

#### 2.4. Cập nhật mẫu trang

- **Endpoint**: `PUT /v1/generic-page-templates/{id}`
- **Path Parameters**:
  - `id`: ID của mẫu trang
- **Request Body**:
  ```json
  {
    "name": "Tên mẫu trang mới",
    "description": "Mô tả mẫu trang mới",
    "category": "Danh mục mới",
    "tags": ["tag1", "tag2", "tag3"],
    "config": {
      // Cấu hình mẫu trang mới (GenericFormConfig)
    }
  }
  ```
- **Response**: `ApiResponseDto<GenericPageTemplate>`

#### 2.5. Xóa mẫu trang

- **Endpoint**: `DELETE /v1/generic-page-templates/{id}`
- **Path Parameters**:
  - `id`: ID của mẫu trang
- **Response**: `ApiResponseDto<void>`

### 3. Xử lý form

#### 3.1. Gửi dữ liệu form

- **Endpoint**: `POST /v1/generic-pages/{id}/submit`
- **Path Parameters**:
  - `id`: ID của trang
- **Request Body**: Dữ liệu form (tùy thuộc vào cấu hình form)
- **Response**: `ApiResponseDto<T>` (T tùy thuộc vào cấu hình API của trang)

## Cấu trúc cơ sở dữ liệu

Dưới đây là đề xuất cấu trúc cơ sở dữ liệu cho module Generic Page:

### 1. Bảng `generic_pages`

Lưu trữ thông tin cơ bản về các trang tùy chỉnh.

| Cột | Kiểu dữ liệu | Mô tả |
|-----|--------------|-------|
| id | VARCHAR(36) | Khóa chính, UUID |
| name | VARCHAR(255) | Tên trang |
| description | TEXT | Mô tả trang |
| path | VARCHAR(255) | Đường dẫn URL của trang (unique) |
| config | JSON | Cấu hình trang dạng JSON |
| status | ENUM('draft', 'published', 'archived') | Trạng thái trang |
| created_at | BIGINT | Thời điểm tạo (Unix timestamp) |
| updated_at | BIGINT | Thời điểm cập nhật (Unix timestamp) |
| published_at | BIGINT | Thời điểm xuất bản (Unix timestamp) |
| created_by | VARCHAR(36) | ID người tạo |
| updated_by | VARCHAR(36) | ID người cập nhật |

### 2. Bảng `generic_page_templates`

Lưu trữ các mẫu trang có thể tái sử dụng.

| Cột | Kiểu dữ liệu | Mô tả |
|-----|--------------|-------|
| id | VARCHAR(36) | Khóa chính, UUID |
| name | VARCHAR(255) | Tên mẫu trang |
| description | TEXT | Mô tả mẫu trang |
| category | VARCHAR(100) | Danh mục của mẫu trang |
| thumbnail | VARCHAR(255) | URL hình thu nhỏ |
| config | JSON | Cấu hình mẫu trang dạng JSON |
| created_at | BIGINT | Thời điểm tạo (Unix timestamp) |
| updated_at | BIGINT | Thời điểm cập nhật (Unix timestamp) |
| created_by | VARCHAR(36) | ID người tạo |
| updated_by | VARCHAR(36) | ID người cập nhật |

### 3. Bảng `generic_page_template_tags`

Lưu trữ các tag cho mẫu trang (quan hệ nhiều-nhiều).

| Cột | Kiểu dữ liệu | Mô tả |
|-----|--------------|-------|
| template_id | VARCHAR(36) | ID mẫu trang (khóa ngoại) |
| tag | VARCHAR(50) | Tên tag |

### 4. Bảng `generic_page_submissions`

Lưu trữ dữ liệu được gửi từ các form trên trang.

| Cột | Kiểu dữ liệu | Mô tả |
|-----|--------------|-------|
| id | VARCHAR(36) | Khóa chính, UUID |
| page_id | VARCHAR(36) | ID trang (khóa ngoại) |
| data | JSON | Dữ liệu form được gửi |
| status | ENUM('pending', 'processed', 'rejected') | Trạng thái xử lý |
| created_at | BIGINT | Thời điểm gửi (Unix timestamp) |
| updated_at | BIGINT | Thời điểm cập nhật (Unix timestamp) |
| ip_address | VARCHAR(45) | Địa chỉ IP người gửi |
| user_agent | TEXT | User agent của người gửi |
| user_id | VARCHAR(36) | ID người dùng (nếu đã đăng nhập) |

## Câu lệnh SQL tạo bảng

```sql
-- Tạo bảng generic_pages
CREATE TABLE generic_pages (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    path VARCHAR(255) NOT NULL,
    config JSON NOT NULL,
    status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft',
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    published_at BIGINT NULL,
    created_by VARCHAR(36),
    updated_by VARCHAR(36),
    UNIQUE KEY unique_path (path)
);

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE generic_pages IS 'Lưu trữ thông tin các trang tùy chỉnh được tạo bởi Generic Page Builder';
COMMENT ON COLUMN generic_pages.id IS 'ID duy nhất của trang, dạng UUID';
COMMENT ON COLUMN generic_pages.name IS 'Tên của trang, hiển thị trong giao diện quản trị';
COMMENT ON COLUMN generic_pages.description IS 'Mô tả chi tiết về trang, hỗ trợ cho việc quản lý';
COMMENT ON COLUMN generic_pages.path IS 'Đường dẫn URL của trang, dùng để truy cập trang từ frontend';
COMMENT ON COLUMN generic_pages.config IS 'Cấu hình trang dạng JSON, bao gồm layout, components, và các thiết lập khác';
COMMENT ON COLUMN generic_pages.status IS 'Trạng thái của trang: draft (nháp), published (đã xuất bản), archived (đã lưu trữ)';
COMMENT ON COLUMN generic_pages.created_at IS 'Thời điểm tạo trang, dạng Unix timestamp (milliseconds)';
COMMENT ON COLUMN generic_pages.updated_at IS 'Thời điểm cập nhật trang gần nhất, dạng Unix timestamp (milliseconds)';
COMMENT ON COLUMN generic_pages.published_at IS 'Thời điểm xuất bản trang, dạng Unix timestamp (milliseconds)';
COMMENT ON COLUMN generic_pages.created_by IS 'ID của người dùng tạo trang';
COMMENT ON COLUMN generic_pages.updated_by IS 'ID của người dùng cập nhật trang gần nhất';

-- Tạo bảng generic_page_templates
CREATE TABLE generic_page_templates (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    thumbnail VARCHAR(255),
    config JSON NOT NULL,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    created_by VARCHAR(36),
    updated_by VARCHAR(36)
);

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE generic_page_templates IS 'Lưu trữ các mẫu trang có thể tái sử dụng để tạo trang mới nhanh chóng';
COMMENT ON COLUMN generic_page_templates.id IS 'ID duy nhất của mẫu trang, dạng UUID';
COMMENT ON COLUMN generic_page_templates.name IS 'Tên của mẫu trang, hiển thị trong giao diện quản trị';
COMMENT ON COLUMN generic_page_templates.description IS 'Mô tả chi tiết về mẫu trang và mục đích sử dụng';
COMMENT ON COLUMN generic_page_templates.category IS 'Danh mục của mẫu trang, dùng để phân loại và lọc';
COMMENT ON COLUMN generic_page_templates.thumbnail IS 'URL hình thu nhỏ minh họa cho mẫu trang';
COMMENT ON COLUMN generic_page_templates.config IS 'Cấu hình mẫu trang dạng JSON, bao gồm layout, components, và các thiết lập khác';
COMMENT ON COLUMN generic_page_templates.created_at IS 'Thời điểm tạo mẫu trang, dạng Unix timestamp (milliseconds)';
COMMENT ON COLUMN generic_page_templates.updated_at IS 'Thời điểm cập nhật mẫu trang gần nhất, dạng Unix timestamp (milliseconds)';
COMMENT ON COLUMN generic_page_templates.created_by IS 'ID của người dùng tạo mẫu trang';
COMMENT ON COLUMN generic_page_templates.updated_by IS 'ID của người dùng cập nhật mẫu trang gần nhất';

-- Tạo bảng generic_page_template_tags
CREATE TABLE generic_page_template_tags (
    template_id VARCHAR(36) NOT NULL,
    tag VARCHAR(50) NOT NULL,
    PRIMARY KEY (template_id, tag),
    FOREIGN KEY (template_id) REFERENCES generic_page_templates(id) ON DELETE CASCADE
);

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE generic_page_template_tags IS 'Lưu trữ các tag cho mẫu trang, hỗ trợ tìm kiếm và phân loại';
COMMENT ON COLUMN generic_page_template_tags.template_id IS 'ID của mẫu trang, tham chiếu đến bảng generic_page_templates';
COMMENT ON COLUMN generic_page_template_tags.tag IS 'Tên tag, dùng để phân loại và tìm kiếm mẫu trang';

-- Tạo bảng generic_page_submissions
CREATE TABLE generic_page_submissions (
    id VARCHAR(36) PRIMARY KEY,
    page_id VARCHAR(36) NOT NULL,
    data JSON NOT NULL,
    status ENUM('pending', 'processed', 'rejected') NOT NULL DEFAULT 'pending',
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    user_id VARCHAR(36),
    FOREIGN KEY (page_id) REFERENCES generic_pages(id) ON DELETE CASCADE
);

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE generic_page_submissions IS 'Lưu trữ dữ liệu được gửi từ các form trên trang tùy chỉnh';
COMMENT ON COLUMN generic_page_submissions.id IS 'ID duy nhất của bản ghi dữ liệu, dạng UUID';
COMMENT ON COLUMN generic_page_submissions.page_id IS 'ID của trang mà dữ liệu được gửi từ đó, tham chiếu đến bảng generic_pages';
COMMENT ON COLUMN generic_page_submissions.data IS 'Dữ liệu form được gửi, dạng JSON';
COMMENT ON COLUMN generic_page_submissions.status IS 'Trạng thái xử lý: pending (đang chờ), processed (đã xử lý), rejected (đã từ chối)';
COMMENT ON COLUMN generic_page_submissions.created_at IS 'Thời điểm gửi dữ liệu, dạng Unix timestamp (milliseconds)';
COMMENT ON COLUMN generic_page_submissions.updated_at IS 'Thời điểm cập nhật trạng thái gần nhất, dạng Unix timestamp (milliseconds)';
COMMENT ON COLUMN generic_page_submissions.ip_address IS 'Địa chỉ IP của người gửi dữ liệu';
COMMENT ON COLUMN generic_page_submissions.user_agent IS 'Thông tin user agent của trình duyệt người gửi';
COMMENT ON COLUMN generic_page_submissions.user_id IS 'ID của người dùng gửi dữ liệu (nếu đã đăng nhập)';

-- Tạo index để tối ưu truy vấn
CREATE INDEX idx_generic_pages_status ON generic_pages(status);
CREATE INDEX idx_generic_pages_created_at ON generic_pages(created_at);
CREATE INDEX idx_generic_page_templates_category ON generic_page_templates(category);
CREATE INDEX idx_generic_page_submissions_status ON generic_page_submissions(status);
CREATE INDEX idx_generic_page_submissions_page_id ON generic_page_submissions(page_id);
```

## Dữ liệu mẫu

### 1. Dữ liệu mẫu cho bảng `generic_pages`

```sql
-- Chèn dữ liệu mẫu cho bảng generic_pages
INSERT INTO generic_pages (id, name, description, path, config, status, created_at, updated_at, published_at, created_by, updated_by)
VALUES
(
    'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    'Trang liên hệ',
    'Form liên hệ cho khách hàng',
    'lien-he',
    '{
        "formId": "contact-form",
        "title": "Liên hệ với chúng tôi",
        "subtitle": "Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn",
        "groups": [
            {
                "id": "contact-info",
                "label": "Thông tin liên hệ",
                "fields": [
                    {
                        "component": "text-input",
                        "config": {
                            "id": "name",
                            "label": "Họ và tên",
                            "required": true,
                            "placeholder": "Nhập họ và tên của bạn",
                            "validation": {
                                "minLength": 2,
                                "maxLength": 100
                            }
                        },
                        "grid": {
                            "i": "name",
                            "x": 0,
                            "y": 0,
                            "w": 12,
                            "h": 1
                        }
                    },
                    {
                        "component": "text-input",
                        "config": {
                            "id": "email",
                            "label": "Email",
                            "type": "email",
                            "required": true,
                            "placeholder": "Nhập địa chỉ email của bạn",
                            "validation": {
                                "pattern": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
                            }
                        },
                        "grid": {
                            "i": "email",
                            "x": 0,
                            "y": 1,
                            "w": 6,
                            "h": 1
                        }
                    },
                    {
                        "component": "text-input",
                        "config": {
                            "id": "phone",
                            "label": "Số điện thoại",
                            "type": "tel",
                            "required": true,
                            "placeholder": "Nhập số điện thoại của bạn",
                            "validation": {
                                "pattern": "^[0-9]{10,11}$"
                            }
                        },
                        "grid": {
                            "i": "phone",
                            "x": 6,
                            "y": 1,
                            "w": 6,
                            "h": 1
                        }
                    },
                    {
                        "component": "text-area",
                        "config": {
                            "id": "message",
                            "label": "Nội dung",
                            "required": true,
                            "placeholder": "Nhập nội dung tin nhắn",
                            "validation": {
                                "minLength": 10,
                                "maxLength": 1000
                            }
                        },
                        "grid": {
                            "i": "message",
                            "x": 0,
                            "y": 2,
                            "w": 12,
                            "h": 3
                        }
                    }
                ]
            }
        ],
        "apiConfig": {
            "enabled": true,
            "endpoint": "/api/v1/contact",
            "method": "POST"
        },
        "uiConfig": {
            "submitButtonText": "Gửi liên hệ",
            "showResetButton": true,
            "resetButtonText": "Xóa form",
            "layout": "card",
            "spacing": "normal",
            "showRequiredIndicator": true
        }
    }',
    'published',
    1672567200000, -- 2023-01-01 10:00:00
    1673363400000, -- 2023-01-10 15:30:00
    1673363400000, -- 2023-01-10 15:30:00
    'admin-user-id',
    'admin-user-id'
),
(
    'a1b2c3d4-e5f6-4a5b-9c3d-2e1f0a9b8c7d',
    'Trang đăng ký sự kiện',
    'Form đăng ký tham gia sự kiện',
    'dang-ky-su-kien',
    '{
        "formId": "event-registration",
        "title": "Đăng ký tham gia sự kiện",
        "subtitle": "Vui lòng điền đầy đủ thông tin để đăng ký tham gia",
        "groups": [
            {
                "id": "personal-info",
                "label": "Thông tin cá nhân",
                "fields": [
                    {
                        "component": "text-input",
                        "config": {
                            "id": "fullName",
                            "label": "Họ và tên",
                            "required": true,
                            "placeholder": "Nhập họ và tên của bạn"
                        },
                        "grid": {
                            "i": "fullName",
                            "x": 0,
                            "y": 0,
                            "w": 12,
                            "h": 1
                        }
                    },
                    {
                        "component": "text-input",
                        "config": {
                            "id": "email",
                            "label": "Email",
                            "type": "email",
                            "required": true,
                            "placeholder": "Nhập địa chỉ email của bạn"
                        },
                        "grid": {
                            "i": "email",
                            "x": 0,
                            "y": 1,
                            "w": 6,
                            "h": 1
                        }
                    },
                    {
                        "component": "text-input",
                        "config": {
                            "id": "phone",
                            "label": "Số điện thoại",
                            "type": "tel",
                            "required": true,
                            "placeholder": "Nhập số điện thoại của bạn"
                        },
                        "grid": {
                            "i": "phone",
                            "x": 6,
                            "y": 1,
                            "w": 6,
                            "h": 1
                        }
                    }
                ]
            },
            {
                "id": "event-details",
                "label": "Thông tin sự kiện",
                "fields": [
                    {
                        "component": "select",
                        "config": {
                            "id": "eventSession",
                            "label": "Chọn buổi tham dự",
                            "required": true,
                            "options": [
                                {"label": "Sáng - 08:00 - 12:00", "value": "morning"},
                                {"label": "Chiều - 13:30 - 17:30", "value": "afternoon"},
                                {"label": "Cả ngày", "value": "full-day"}
                            ]
                        },
                        "grid": {
                            "i": "eventSession",
                            "x": 0,
                            "y": 0,
                            "w": 12,
                            "h": 1
                        }
                    },
                    {
                        "component": "checkbox",
                        "config": {
                            "id": "specialRequirements",
                            "label": "Yêu cầu đặc biệt",
                            "options": [
                                {"label": "Chế độ ăn chay", "value": "vegetarian"},
                                {"label": "Hỗ trợ người khuyết tật", "value": "disability-support"},
                                {"label": "Phiên dịch", "value": "translation"}
                            ]
                        },
                        "grid": {
                            "i": "specialRequirements",
                            "x": 0,
                            "y": 1,
                            "w": 12,
                            "h": 2
                        }
                    },
                    {
                        "component": "text-area",
                        "config": {
                            "id": "notes",
                            "label": "Ghi chú",
                            "placeholder": "Nhập ghi chú nếu có"
                        },
                        "grid": {
                            "i": "notes",
                            "x": 0,
                            "y": 3,
                            "w": 12,
                            "h": 2
                        }
                    }
                ]
            }
        ],
        "uiConfig": {
            "submitButtonText": "Đăng ký",
            "showResetButton": true,
            "resetButtonText": "Làm mới",
            "layout": "card",
            "spacing": "normal"
        }
    }',
    'draft',
    1676453100000, -- 2023-02-15 09:45:00
    1676902800000, -- 2023-02-20 14:20:00
    NULL,
    'admin-user-id',
    'admin-user-id'
);
```

### 2. Dữ liệu mẫu cho bảng `generic_page_templates`

```sql
-- Chèn dữ liệu mẫu cho bảng generic_page_templates
INSERT INTO generic_page_templates (id, name, description, category, thumbnail, config, created_at, updated_at, created_by, updated_by)
VALUES
(
    'e2f3a4b5-c6d7-4e5f-8a9b-0c1d2e3f4a5b',
    'Mẫu form liên hệ',
    'Mẫu form liên hệ cơ bản với các trường thông tin liên hệ',
    'Form',
    '/assets/images/templates/contact-form.jpg',
    '{
        "formId": "contact-form-template",
        "title": "Liên hệ với chúng tôi",
        "subtitle": "Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn",
        "groups": [
            {
                "id": "contact-info",
                "label": "Thông tin liên hệ",
                "fields": [
                    {
                        "component": "text-input",
                        "config": {
                            "id": "name",
                            "label": "Họ và tên",
                            "required": true,
                            "placeholder": "Nhập họ và tên của bạn"
                        },
                        "grid": {
                            "i": "name",
                            "x": 0,
                            "y": 0,
                            "w": 12,
                            "h": 1
                        }
                    },
                    {
                        "component": "text-input",
                        "config": {
                            "id": "email",
                            "label": "Email",
                            "type": "email",
                            "required": true,
                            "placeholder": "Nhập địa chỉ email của bạn"
                        },
                        "grid": {
                            "i": "email",
                            "x": 0,
                            "y": 1,
                            "w": 6,
                            "h": 1
                        }
                    },
                    {
                        "component": "text-input",
                        "config": {
                            "id": "phone",
                            "label": "Số điện thoại",
                            "type": "tel",
                            "required": true,
                            "placeholder": "Nhập số điện thoại của bạn"
                        },
                        "grid": {
                            "i": "phone",
                            "x": 6,
                            "y": 1,
                            "w": 6,
                            "h": 1
                        }
                    },
                    {
                        "component": "text-area",
                        "config": {
                            "id": "message",
                            "label": "Nội dung",
                            "required": true,
                            "placeholder": "Nhập nội dung tin nhắn"
                        },
                        "grid": {
                            "i": "message",
                            "x": 0,
                            "y": 2,
                            "w": 12,
                            "h": 3
                        }
                    }
                ]
            }
        ],
        "uiConfig": {
            "submitButtonText": "Gửi liên hệ",
            "showResetButton": true,
            "resetButtonText": "Xóa form",
            "layout": "card",
            "spacing": "normal"
        }
    }',
    1672918200000, -- 2023-01-05 11:30:00
    1672918200000, -- 2023-01-05 11:30:00
    'admin-user-id',
    'admin-user-id'
),
(
    'b5c6d7e8-f9a0-4b1c-8d2e-3f4a5b6c7d8e',
    'Mẫu trang giới thiệu',
    'Mẫu trang giới thiệu sản phẩm hoặc dịch vụ',
    'Trang thông tin',
    '/assets/images/templates/product-intro.jpg',
    '{
        "formId": "product-intro-template",
        "title": "Giới thiệu sản phẩm",
        "subtitle": "Thông tin chi tiết về sản phẩm của chúng tôi",
        "groups": [
            {
                "id": "product-header",
                "label": "Tiêu đề sản phẩm",
                "fields": [
                    {
                        "component": "typography",
                        "config": {
                            "id": "product-title",
                            "label": "Tên sản phẩm",
                            "variant": "h1",
                            "defaultValue": "Tên sản phẩm của bạn"
                        },
                        "grid": {
                            "i": "product-title",
                            "x": 0,
                            "y": 0,
                            "w": 12,
                            "h": 1
                        }
                    },
                    {
                        "component": "image",
                        "config": {
                            "id": "product-image",
                            "label": "Hình ảnh sản phẩm",
                            "defaultValue": "/assets/images/placeholder.jpg",
                            "alt": "Hình ảnh sản phẩm"
                        },
                        "grid": {
                            "i": "product-image",
                            "x": 0,
                            "y": 1,
                            "w": 12,
                            "h": 6
                        }
                    }
                ]
            },
            {
                "id": "product-details",
                "label": "Chi tiết sản phẩm",
                "fields": [
                    {
                        "component": "typography",
                        "config": {
                            "id": "product-description",
                            "label": "Mô tả sản phẩm",
                            "variant": "body1",
                            "defaultValue": "Mô tả chi tiết về sản phẩm của bạn ở đây."
                        },
                        "grid": {
                            "i": "product-description",
                            "x": 0,
                            "y": 0,
                            "w": 12,
                            "h": 3
                        }
                    },
                    {
                        "component": "divider",
                        "config": {
                            "id": "divider-1"
                        },
                        "grid": {
                            "i": "divider-1",
                            "x": 0,
                            "y": 3,
                            "w": 12,
                            "h": 1
                        }
                    },
                    {
                        "component": "typography",
                        "config": {
                            "id": "features-title",
                            "label": "Tính năng",
                            "variant": "h2",
                            "defaultValue": "Tính năng nổi bật"
                        },
                        "grid": {
                            "i": "features-title",
                            "x": 0,
                            "y": 4,
                            "w": 12,
                            "h": 1
                        }
                    }
                ]
            }
        ],
        "uiConfig": {
            "layout": "card",
            "spacing": "relaxed"
        }
    }',
    1676036100000, -- 2023-02-10 14:15:00
    1676192400000, -- 2023-02-12 09:30:00
    'admin-user-id',
    'admin-user-id'
);
```

### 3. Dữ liệu mẫu cho bảng `generic_page_template_tags`

```sql
-- Chèn dữ liệu mẫu cho bảng generic_page_template_tags
INSERT INTO generic_page_template_tags (template_id, tag)
VALUES
('e2f3a4b5-c6d7-4e5f-8a9b-0c1d2e3f4a5b', 'form'),
('e2f3a4b5-c6d7-4e5f-8a9b-0c1d2e3f4a5b', 'liên hệ'),
('e2f3a4b5-c6d7-4e5f-8a9b-0c1d2e3f4a5b', 'cơ bản'),
('b5c6d7e8-f9a0-4b1c-8d2e-3f4a5b6c7d8e', 'giới thiệu'),
('b5c6d7e8-f9a0-4b1c-8d2e-3f4a5b6c7d8e', 'sản phẩm'),
('b5c6d7e8-f9a0-4b1c-8d2e-3f4a5b6c7d8e', 'landing page');
```

### 4. Dữ liệu mẫu cho bảng `generic_page_submissions`

```sql
-- Chèn dữ liệu mẫu cho bảng generic_page_submissions
INSERT INTO generic_page_submissions (id, page_id, data, status, created_at, updated_at, ip_address, user_agent, user_id)
VALUES
(
    'c7d8e9f0-a1b2-4c3d-9e0f-1a2b3c4d5e6f',
    'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    '{
        "name": "Nguyễn Văn A",
        "email": "<EMAIL>",
        "phone": "0901234567",
        "message": "Tôi muốn biết thêm thông tin về sản phẩm của công ty."
    }',
    'processed',
    1678439700000, -- 2023-03-10 09:15:00
    1678444200000, -- 2023-03-10 10:30:00
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    NULL
),
(
    'd8e9f0a1-b2c3-4d5e-6f7a-8b9c0d1e2f3a',
    'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    '{
        "name": "Trần Thị B",
        "email": "<EMAIL>",
        "phone": "0912345678",
        "message": "Tôi cần tư vấn về dịch vụ khách hàng."
    }',
    'pending',
    1678887600000, -- 2023-03-15 14:20:00
    1678887600000, -- 2023-03-15 14:20:00
    '*************',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    NULL
);
```
