import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { GenericPageSubmissionStatusEnum } from '../constants/generic-page.enum';
import { GenericPage } from './generic-page.entity';

/**
 * Entity đại diện cho bảng generic_page_submissions trong cơ sở dữ liệu
 * Bảng lưu trữ dữ liệu được gửi từ các form trên trang
 */
@Entity('generic_page_submissions')
export class GenericPageSubmission {
  /**
   * ID duy nhất của bản ghi dữ liệu, dạng UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID của trang mà dữ liệu được gửi từ đó, tham chiếu đến bảng generic_pages
   */
  @Column({ name: 'page_id', length: 36 })
  pageId: string;

  /**
   * Dữ liệu form đư<PERSON><PERSON> gử<PERSON>, dạng JSON
   */
  @Column({ type: 'json' })
  data: Record<string, any>;

  /**
   * Trạng thái xử lý: pending (đang chờ), processed (đã xử lý), rejected (đã từ chối)
   */
  @Column({
    type: 'enum',
    enum: GenericPageSubmissionStatusEnum,
    default: GenericPageSubmissionStatusEnum.PENDING,
  })
  status: GenericPageSubmissionStatusEnum;

  /**
   * Thời điểm gửi dữ liệu, dạng Unix timestamp (milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật trạng thái gần nhất, dạng Unix timestamp (milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  /**
   * Địa chỉ IP của người gửi dữ liệu
   */
  @Column({ name: 'ip_address', length: 45, nullable: true })
  ipAddress: string;

  /**
   * Thông tin user agent của trình duyệt người gửi
   */
  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string;

  /**
   * ID của người dùng gửi dữ liệu (nếu đã đăng nhập)
   */
  @Column({ name: 'user_id', length: 36, nullable: true })
  userId: string;

  /**
   * Quan hệ với trang
   */
  @ManyToOne(() => GenericPage, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'page_id' })
  page: GenericPage;
}
