import { Body, Controller, Get, HttpStatus, Post, Delete, Param, Query, UseGuards, Patch } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { UserWebsiteUserService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { CreateWebsiteDto, WebsiteQueryDto, WebsiteResponseDto, DeleteWebsitesDto, WebsiteScriptResponseDto, CreateWebsiteResponseDto, UpdateWebsiteLogoDto } from '../dto';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';

@ApiTags(SWAGGER_API_TAGS.USER_INTEGRATION_WEBSITE)
@Controller('integration/website')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserWebsiteUserController {
  constructor(
    private readonly userWebsiteUserService: UserWebsiteUserService,
  ) {}

  /**
   * Lấy danh sách website của người dùng
   */
  @ApiOperation({ summary: 'Lấy danh sách website của người dùng' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang hiện tại (bắt đầu từ 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số lượng bản ghi trên mỗi trang',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Từ khóa tìm kiếm theo tên website',
  })
  @ApiQuery({
    name: 'verify',
    required: false,
    type: Boolean,
    description: 'Lọc theo trạng thái xác minh',
  })
  @ApiQuery({
    name: 'hasAgent',
    required: false,
    type: Boolean,
    description: 'Lọc theo trạng thái gán agent (true: đã gán agent, false: chưa gán agent)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách website thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy danh sách website thành công' },
        result: {
          type: 'object',
          properties: {
            items: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: '86c238b5-ab20-45ee-9eb2-fa75a40e9751' },
                  host: { type: 'string', example: 'redai.vn' },
                  verify: { type: 'boolean', example: false },
                  agentId: { type: 'string', nullable: true, example: null },
                  agentName: { type: 'string', nullable: true, example: null },
                  logo: { type: 'string', nullable: true, example: null },
                  createdAt: { type: 'string', example: '2025-05-25T09:03:11.444Z' }
                }
              }
            },
            meta: {
              type: 'object',
              properties: {
                totalItems: { type: 'number' },
                itemCount: { type: 'number' },
                itemsPerPage: { type: 'number' },
                totalPages: { type: 'number' },
                currentPage: { type: 'number' }
              }
            }
          }
        }
      }
    }
  })
  @Get()
  async findAll(
    @Query() queryDto: WebsiteQueryDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<PaginatedResult<WebsiteResponseDto>>> {
    const result = await this.userWebsiteUserService.findAll(queryDto, user.id);
    return wrapResponse(result, 'Lấy danh sách website thành công');
  }

  /**
   * Tạo mới website
   */
  @ApiOperation({
    summary: 'Tạo mới website',
    description: 'Tạo mới website và tạo presigned URL cho upload logo (nếu có logoMime)'
  })
  @ApiBody({
    description: 'Thông tin website cần tạo',
    type: CreateWebsiteDto
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo website thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Tạo website thành công' },
        result: {
          type: 'object',
          properties: {
            logoUploadUrl: {
              type: 'string',
              nullable: true,
              example: 'https://s3.amazonaws.com/bucket/path/to/upload?signature=...',
              description: 'Presigned URL để upload logo (chỉ có khi logoMime được cung cấp)'
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ hoặc website đã tồn tại',
  })
  @Post()
  async create(
    @Body() createWebsiteDto: CreateWebsiteDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<CreateWebsiteResponseDto>> {
    const result = await this.userWebsiteUserService.create(createWebsiteDto, user.id);
    return wrapResponse(result, 'Tạo website thành công');
  }

  /**
   * Cập nhật logo S3 key cho website sau khi upload thành công
   */
  @Patch(':websiteId/logo')
  @ApiOperation({
    summary: 'Cập nhật logo website',
    description: 'Cập nhật S3 key của logo sau khi upload thành công'
  })
  @ApiParam({
    name: 'websiteId',
    description: 'UUID của website cần cập nhật logo',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiBody({
    description: 'S3 key của logo đã upload',
    type: UpdateWebsiteLogoDto
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật logo thành công',
    schema: ApiResponseDto.getSchema('Cập nhật logo thành công')
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Website không tồn tại',
  })
  async updateLogo(
    @Param('websiteId') websiteId: string,
    @Body() updateLogoDto: UpdateWebsiteLogoDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<null>> {
    await this.userWebsiteUserService.updateWebsiteLogo(
      websiteId,
      user.id,
      updateLogoDto.logoS3Key
    );
    return ApiResponseDto.success(null, 'Cập nhật logo thành công');
  }

  /**
   * Xóa một website
   */
  @Delete(':websiteId')
  @ApiOperation({ summary: 'Xóa một website' })
  @ApiParam({
    name: 'websiteId',
    description: 'UUID của website cần xóa',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa website thành công',
    schema: ApiResponseDto.getSchema('Xóa website thành công')
  })
  @ApiErrorResponse(
    INTEGRATION_ERROR_CODES.WEBSITE_NOT_FOUND,
    INTEGRATION_ERROR_CODES.WEBSITE_DELETE_FAILED,
    INTEGRATION_ERROR_CODES.WEBSITE_ACCESS_DENIED
  )
  async deleteWebsite(
    @Param('websiteId') websiteId: string,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<null>> {
    await this.userWebsiteUserService.deleteWebsite(websiteId, user.id);
    return ApiResponseDto.success(null, 'Xóa website thành công');
  }

  /**
   * Xóa nhiều website cùng lúc
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa nhiều website cùng lúc' })
  @ApiBody({
    description: 'Danh sách UUID của các website cần xóa',
    type: DeleteWebsitesDto
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa các website thành công',
    schema: {
      properties: {
        deletedCount: { type: 'number', example: 2 },
        errorWebsites: { type: 'array', items: { type: 'string' }, example: ['Website A', 'Website B'] }
      }
    }
  })
  @ApiErrorResponse(
    INTEGRATION_ERROR_CODES.WEBSITE_DELETE_FAILED
  )
  async deleteMultipleWebsites(
    @Body() deleteDto: DeleteWebsitesDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<{ deletedCount: number; errorWebsites?: string[] }>> {
    const result = await this.userWebsiteUserService.deleteMultipleWebsites(deleteDto, user.id);
    return ApiResponseDto.success(result, 'Xóa các website thành công');
  }

  /**
   * Lấy widget script tích hợp cho website
   */
  @Get('script')
  @ApiOperation({
    summary: 'Lấy widget script tích hợp cho website',
    description: 'Tạo widget script HTML để embed vào website cho tích hợp với RedAI'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy widget script thành công',
    type: WebsiteScriptResponseDto
  })
  async getWebsiteScript(
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<WebsiteScriptResponseDto>> {
    const result = await this.userWebsiteUserService.getWebsiteScript(user.id);
    return ApiResponseDto.success(result, 'Lấy widget script thành công');
  }
}
