import { ApiProperty } from '@nestjs/swagger';
import { WhatsAppTemplateCategory } from './create-whatsapp-template.dto';

/**
 * DTO cho việc trả về thông tin mẫu tin nhắn WhatsApp
 */
export class WhatsAppTemplateResponseDto {
  @ApiProperty({
    description: 'ID của mẫu tin nhắn',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID tài khoản WhatsApp',
    example: 1,
  })
  whatsappAccountId: number;

  @ApiProperty({
    description: 'ID mẫu tin nhắn trên WhatsApp',
    example: '***************',
  })
  templateId: string;

  @ApiProperty({
    description: 'Tên mẫu tin nhắn',
    example: 'welcome_message',
  })
  name: string;

  @ApiProperty({
    description: 'Ngôn ngữ của mẫu tin nhắn',
    example: 'vi',
  })
  language: string;

  @ApiProperty({
    description: 'Loại mẫu tin nhắn',
    enum: WhatsAppTemplateCategory,
    example: WhatsAppTemplateCategory.UTILITY,
  })
  category: WhatsAppTemplateCategory;

  @ApiProperty({
    description: 'Trạng thái của mẫu tin nhắn',
    example: 'APPROVED',
    enum: ['APPROVED', 'PENDING', 'REJECTED'],
  })
  status: string;

  @ApiProperty({
    description: 'Các thành phần của mẫu tin nhắn',
    example: {
      header: {
        type: 'text',
        text: 'Chào mừng đến với RedAI',
      },
      body: {
        type: 'text',
        text: 'Xin chào {{1}}, cảm ơn bạn đã liên hệ với chúng tôi.',
      },
      footer: {
        type: 'text',
        text: 'RedAI - Trợ lý AI thông minh',
      },
      buttons: [
        {
          type: 'url',
          text: 'Tìm hiểu thêm',
          url: 'https://redai.vn',
        },
      ],
    },
  })
  components: Record<string, any>;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1623456789,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: 1623456789,
  })
  updatedAt: number;
}
