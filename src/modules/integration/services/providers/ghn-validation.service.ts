import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  getProviderBaseUrl,
  getProviderTimeout,
  PROVIDER_TEST_DATA,
  PROVIDER_TEST_ENDPOINTS
} from '../../constants/provider-config';
import { GHNProviderConfig } from '../../interfaces';
import { TestShipmentProviderResponseDto } from '../../user/dto/shipment';
import { HttpClientService } from '../http-client.service';

/**
 * Service validation cho GHN (Giao Hàng Nhanh)
 */
@Injectable()
export class GHNValidationService {
  private readonly logger = new Logger(GHNValidationService.name);
  private readonly baseUrl: string;
  private readonly timeout: number;

  constructor(
    private readonly httpClient: HttpClientService,
    private readonly configService: ConfigService,
  ) {
    // Lấy base URL từ env hoặc dùng default
    const envUrl = this.configService.get<string>('GHN_BASE_URL');
    const isProduction = this.configService.get<string>('NODE_ENV') === 'production';

    this.baseUrl = envUrl || getProviderBaseUrl('GHN', isProduction);
    this.timeout = getProviderTimeout('GHN');

    this.logger.log(`GHN Validation Service initialized with URL: ${this.baseUrl}`);
  }

  /**
   * Test connection với GHN API
   */
  async testConnection(config: GHNProviderConfig): Promise<TestShipmentProviderResponseDto> {
    try {
      this.logger.debug('Testing GHN connection', { shopId: config.shopId });

      // Validate input
      if (!config.token || !config.shopId) {
        return {
          success: false,
          message: 'Token và Shop ID là bắt buộc cho GHN',
        };
      }

      // Test 1: Gọi API lấy danh sách shops để validate token
      const shopsResponse = await this.getShops(config.token);

      if (!shopsResponse.success) {
        return shopsResponse;
      }

      // Test 2: Kiểm tra shopId có tồn tại trong danh sách shops
      const shops = shopsResponse.data?.shops || [];
      const shopExists = shops.some(shop => shop._id?.toString() === config.shopId);

      if (!shopExists) {
        this.logger.warn('Shop ID not found in GHN account', {
          shopId: config.shopId,
          availableShops: shops.map(s => ({ id: s._id, name: s.name }))
        });

        return {
          success: false,
          message: 'Shop ID không tồn tại trong tài khoản GHN của bạn',
        };
      }

      // Lấy thông tin shop để trả về
      const shop = shops.find(s => s._id?.toString() === config.shopId);

      this.logger.log('GHN connection test successful', {
        shopId: config.shopId,
        shopName: shop?.name
      });

      return {
        success: true,
        message: `Kết nối GHN thành công. Shop: ${shop?.name || config.shopId}`,
        data: {
          shopInfo: {
            id: shop?._id,
            name: shop?.name,
            phone: shop?.phone,
            address: shop?.address,
            status: shop?.status,
          }
        }
      };

    } catch (error) {
      this.logger.error('GHN connection test failed', error);

      return {
        success: false,
        message: `Lỗi kết nối GHN: ${error.message}`,
      };
    }
  }

  /**
   * Lấy danh sách shops từ GHN
   */
  private async getShops(token: string): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      const url = `${this.baseUrl}${PROVIDER_TEST_ENDPOINTS.GHN.GET_SHOPS}`;
      const testData = PROVIDER_TEST_DATA.GHN.TEST_SHOP_QUERY;

      const response = await this.httpClient.post(url, testData, {
        headers: {
          'Token': token,
          'Content-Type': 'application/json',
        },
        timeout: this.timeout,
      });

      // Kiểm tra response structure theo GHN API format
      if (response.status === 200 && response.data?.code === 200) {
        return {
          success: true,
          message: 'Success',
          data: response.data.data,
        };
      }

      // Handle GHN API errors
      const errorMessage = response.data?.message || 'Unknown GHN API error';
      const errorCode = response.data?.code;

      this.logger.warn('GHN API returned error', {
        status: response.status,
        code: errorCode,
        message: errorMessage
      });

      // Map GHN error codes to user-friendly messages
      if (errorCode === 401 || errorMessage.includes('Unauthorized')) {
        return {
          success: false,
          message: 'Token GHN không hợp lệ hoặc đã hết hạn',
        };
      }

      return {
        success: false,
        message: `Lỗi GHN API: ${errorMessage}`,
      };

    } catch (error) {
      this.logger.error('Error calling GHN shops API', error);

      // Handle network/timeout errors
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        return {
          success: false,
          message: 'Hết thời gian chờ kết nối đến GHN',
        };
      }

      if (error.response?.status === 401) {
        return {
          success: false,
          message: 'Token GHN không hợp lệ',
        };
      }

      if (error.response?.status >= 500) {
        return {
          success: false,
          message: 'Dịch vụ GHN đang bảo trì, vui lòng thử lại sau',
        };
      }

      return {
        success: false,
        message: `Lỗi kết nối GHN: ${error.message}`,
      };
    }
  }

  /**
   * Test thêm bằng cách gọi API lấy provinces (optional)
   */
  async testGetProvinces(token: string): Promise<boolean> {
    try {
      const url = `${this.baseUrl}${PROVIDER_TEST_ENDPOINTS.GHN.GET_PROVINCES}`;

      const response = await this.httpClient.get(url, {
        headers: {
          'Token': token,
          'Content-Type': 'application/json',
        },
        timeout: this.timeout,
      });

      return response.status === 200 && response.data?.code === 200;
    } catch (error) {
      this.logger.debug('GHN provinces test failed', error.message);
      return false;
    }
  }
}
