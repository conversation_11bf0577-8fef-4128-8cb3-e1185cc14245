import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Lưu trữ thông tin cấu hình cụ thể cho máy chủ <PERSON>ail (SMTP server)
 */
@Entity({ name: 'admin_email_server_configurations' })
export class AdminEmailServerConfigurationEntity {
  /**
   * Primary key
   */
  @PrimaryGeneratedColumn({ type: 'integer' })
  id: number;

  /**
   * Tên hiển thị của cấu hình, ví dụ: "Mailgun Server #1" hoặc "AWS SES"
   */
  @Column({ name: 'server_name', type: 'varchar', length: 100, nullable: true })
  serverName: string;

  /**
   * Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…
   */
  @Column({ name: 'host', type: 'varchar', length: 255, nullable: true })
  host: string;

  /**
   * <PERSON><PERSON>ng <PERSON>TP, ví dụ: 465, 587, …
   */
  @Column({ name: 'port', type: 'integer', nullable: true })
  port: number;

  /**
   * Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng)
   */
  @Column({ name: 'username', type: 'varchar', length: 255, nullable: true })
  username: string;

  /**
   * Mật khẩu hoặc token xác thực cho SMTP
   */
  @Column({ name: 'password', type: 'varchar', length: 255, nullable: true })
  password: string;

  /**
   * Xác định có sử dụng SSL/TLS hay không
   */
  @Column({ name: 'use_ssl', type: 'boolean', nullable: true })
  useSsl: boolean;

  /**
   * Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.
   */
  @Column({ name: 'additional_settings', type: 'json', nullable: true })
  additionalSettings: Record<string, any>;

  /**
   * Nhân viên tạo
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
