import { ApiProperty } from '@nestjs/swagger';
import { Subscription } from '@modules/subscription/entities';
import {SubscriptionStatus} from "@modules/subscription/enums";

export class PlanInfoDto {
  @ApiProperty({
    description: 'ID của gói dịch vụ',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Basic Plan',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả gói dịch vụ',
    example: '<PERSON><PERSON>i cơ bản cho người dùng mới',
    nullable: true,
  })
  description: string;

  @ApiProperty({
    description: 'Loại gói dịch vụ',
    example: 'TIME_ONLY',
    nullable: true,
  })
  packageType?: string;
}

export class PricingInfoDto {
  @ApiProperty({
    description: 'Chu kỳ thanh toán',
    example: 'MONTHLY',
  })
  billingCycle: string;

  @ApiProperty({
    description: 'Gi<PERSON>',
    example: 9.99,
  })
  price: number;

  @ApiProperty({
    description: 'Giới hạn sử dụng',
    example: 1000,
    nullable: true,
  })
  usageLimit?: number;

  @ApiProperty({
    description: 'Đơn vị sử dụng',
    example: 'API_CALLS',
    nullable: true,
  })
  usageUnit?: string;
}

export class SubscriptionResponseDto {
  @ApiProperty({
    description: 'ID của đăng ký',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 10,
  })
  userId: number;

  @ApiProperty({
    description: 'ID của tùy chọn giá',
    example: 1,
  })
  planPricingId: number;

  @ApiProperty({
    description: 'Thông tin gói dịch vụ',
    type: PlanInfoDto,
    nullable: true,
  })
  planInfo?: PlanInfoDto;

  @ApiProperty({
    description: 'Thông tin tùy chọn giá',
    type: PricingInfoDto,
    nullable: true,
  })
  pricingInfo?: PricingInfoDto;

  @ApiProperty({
    description: 'Ngày bắt đầu (Unix timestamp)',
    example: 1632474086123,
  })
  startDate: number;

  @ApiProperty({
    description: 'Ngày kết thúc (Unix timestamp)',
    example: 1635066086123,
  })
  endDate: number;

  @ApiProperty({
    description: 'Tự động gia hạn',
    example: true,
  })
  autoRenew: boolean;

  @ApiProperty({
    description: 'Trạng thái đăng ký',
    enum: SubscriptionStatus,
    example: SubscriptionStatus.ACTIVE,
  })
  status: SubscriptionStatus;

  @ApiProperty({
    description: 'Giới hạn sử dụng',
    example: 1000,
    nullable: true,
  })
  usageLimit?: number;

  @ApiProperty({
    description: 'Sử dụng hiện tại',
    example: 100,
    nullable: true,
  })
  currentUsage?: number;

  @ApiProperty({
    description: 'Giá trị còn lại',
    example: 900,
    nullable: true,
  })
  remainingValue?: number;

  @ApiProperty({
    description: 'Đơn vị sử dụng',
    example: 'API_CALLS',
    nullable: true,
  })
  usageUnit?: string;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1632474086123,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: 1632474086123,
  })
  updatedAt: number;

  constructor(subscription: Subscription, planInfo?: PlanInfoDto, pricingInfo?: PricingInfoDto) {
    this.id = subscription.id;
    this.userId = subscription.userId;
    this.planPricingId = subscription.planPricingId;
    this.startDate = subscription.startDate;
    this.endDate = subscription.endDate;
    this.autoRenew = subscription.autoRenew;
    this.status = subscription.status;
    this.usageLimit = subscription.usageLimit;
    this.currentUsage = subscription.currentUsage;
    this.remainingValue = subscription.remainingValue;
    this.usageUnit = subscription.usageUnit;
    this.createdAt = subscription.createdAt;
    this.updatedAt = subscription.updatedAt;

    if (planInfo) {
      this.planInfo = planInfo;
    }

    if (pricingInfo) {
      this.pricingInfo = pricingInfo;
    }
  }
}
