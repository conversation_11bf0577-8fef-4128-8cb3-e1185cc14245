import { ApiProperty } from '@nestjs/swagger';
import { Plan } from '../entities/plan.entity';
import {PackageType} from "@modules/subscription/enums";

export class PlanResponseDto {
  @ApiProperty({
    description: 'ID của gói dịch vụ',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Basic Plan',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả gói dịch vụ',
    example: '<PERSON><PERSON>i cơ bản cho người dùng mới',
    nullable: true,
  })
  description: string;

  @ApiProperty({
    description: 'Loại gói dịch vụ',
    enum: PackageType,
    example: PackageType.TIME_ONLY,
  })
  packageType: PackageType;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1632474086123,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: 1632474086123,
  })
  updatedAt: number;

  constructor(plan: Plan) {
    this.id = plan.id;
    this.name = plan.name;
    this.description = plan.description;
    this.packageType = plan.packageType;
    this.createdAt = plan.createdAt;
    this.updatedAt = plan.updatedAt;
  }
}
