import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';

// Mock interfaces
enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
  PENDING = 'PENDING'
}

interface Subscription {
  id: number;
  userId: number;
  planPricingId: number;
  startDate: number;
  endDate: number;
  autoRenew: boolean;
  status: SubscriptionStatus;
  usageLimit?: number;
  currentUsage?: number;
  remainingValue?: number;
  usageUnit?: string;
  createdAt: number;
  updatedAt: number;
}

interface UsageLog {
  id: number;
  subscriptionId: number;
  feature: string;
  amount: number;
  usageTime: number;
  createdAt: number;
}

interface PlanInfo {
  id: number;
  name: string;
  description: string;
  packageType?: string;
}

interface PricingInfo {
  billingCycle: string;
  price: number;
  usageLimit?: number;
  usageUnit?: string;
}

interface PaginatedResult<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

interface ApiResponse<T = any> {
  code: number;
  message: string;
  result: T;
}

interface JwtPayload {
  id: number;
  email: string;
  role: string;
}

// Mock service
class MockSubscriptionUserService {
  async findSubscriptions(
    userId: number,
    status: SubscriptionStatus | null,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResult<Subscription>> {
    const mockSubscription: Subscription = {
      id: 1,
      userId,
      planPricingId: 1,
      startDate: Date.now(),
      endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      autoRenew: true,
      status: SubscriptionStatus.ACTIVE,
      usageLimit: 1000,
      currentUsage: 100,
      remainingValue: 900,
      usageUnit: 'API_CALLS',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const mockSubscriptionList: Subscription[] = [
      mockSubscription,
      {
        ...mockSubscription,
        id: 2,
        planPricingId: 2,
        status: SubscriptionStatus.CANCELLED
      }
    ];

    // Filter by status if provided
    const filteredItems = status 
      ? mockSubscriptionList.filter(item => item.status === status)
      : mockSubscriptionList;

    return {
      items: filteredItems,
      meta: {
        totalItems: filteredItems.length,
        itemCount: filteredItems.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(filteredItems.length / limit),
        currentPage: page
      }
    };
  }

  async findSubscriptionWithDetails(id: number): Promise<{
    subscription: Subscription;
    planInfo: PlanInfo;
    pricingInfo: PricingInfo;
  }> {
    if (id === 999) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    return {
      subscription: {
        id,
        userId: 10,
        planPricingId: 1,
        startDate: Date.now(),
        endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
        autoRenew: true,
        status: SubscriptionStatus.ACTIVE,
        usageLimit: 1000,
        currentUsage: 100,
        remainingValue: 900,
        usageUnit: 'API_CALLS',
        createdAt: Date.now(),
        updatedAt: Date.now()
      },
      planInfo: {
        id: 1,
        name: 'Basic Plan',
        description: 'Basic plan for new users',
        packageType: 'TIME_ONLY'
      },
      pricingInfo: {
        billingCycle: 'MONTHLY',
        price: 9.99,
        usageLimit: 1000,
        usageUnit: 'API_CALLS'
      }
    };
  }

  async createSubscription(
    userId: number,
    planPricingId: number,
    autoRenew: boolean = true
  ): Promise<Subscription> {
    if (planPricingId === 999) {
      throw new NotFoundException(`PlanPricing with ID ${planPricingId} not found or not active`);
    }

    if (userId === 999) {
      throw new BadRequestException('User already has active subscriptions');
    }

    return {
      id: 1,
      userId,
      planPricingId,
      startDate: Date.now(),
      endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      autoRenew,
      status: SubscriptionStatus.ACTIVE,
      usageLimit: 1000,
      currentUsage: 0,
      remainingValue: 1000,
      usageUnit: 'API_CALLS',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  async cancelSubscription(id: number): Promise<Subscription> {
    if (id === 999) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    if (id === 888) {
      throw new BadRequestException('Only active subscriptions can be cancelled');
    }

    return {
      id,
      userId: 10,
      planPricingId: 1,
      startDate: Date.now(),
      endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      autoRenew: true,
      status: SubscriptionStatus.CANCELLED,
      usageLimit: 1000,
      currentUsage: 100,
      remainingValue: 900,
      usageUnit: 'API_CALLS',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  async updateAutoRenew(id: number, autoRenew: boolean): Promise<Subscription> {
    if (id === 999) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    if (id === 888) {
      throw new BadRequestException('Only active subscriptions can be updated');
    }

    return {
      id,
      userId: 10,
      planPricingId: 1,
      startDate: Date.now(),
      endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      autoRenew,
      status: SubscriptionStatus.ACTIVE,
      usageLimit: 1000,
      currentUsage: 100,
      remainingValue: 900,
      usageUnit: 'API_CALLS',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  async changePlan(
    id: number,
    newPlanPricingId: number,
    effectiveImmediately: boolean = false
  ): Promise<Subscription> {
    if (id === 999) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    if (id === 888) {
      throw new BadRequestException('Only active subscriptions can be changed');
    }

    if (newPlanPricingId === 999) {
      throw new NotFoundException(`PlanPricing with ID ${newPlanPricingId} not found or not active`);
    }

    return {
      id,
      userId: 10,
      planPricingId: newPlanPricingId,
      startDate: Date.now(),
      endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      autoRenew: true,
      status: SubscriptionStatus.ACTIVE,
      usageLimit: 2000,
      currentUsage: 0,
      remainingValue: 2000,
      usageUnit: 'API_CALLS',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  async findUsageLogs(
    subscriptionId: number,
    startDate: number | null,
    endDate: number | null,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResult<UsageLog>> {
    if (subscriptionId === 999) {
      throw new NotFoundException(`Subscription with ID ${subscriptionId} not found`);
    }

    const mockUsageLog: UsageLog = {
      id: 1,
      subscriptionId,
      feature: 'API_CALL',
      amount: 5,
      usageTime: Date.now() - 2 * 24 * 60 * 60 * 1000,
      createdAt: Date.now() - 2 * 24 * 60 * 60 * 1000
    };

    const mockUsageLogList: UsageLog[] = [
      mockUsageLog,
      {
        ...mockUsageLog,
        id: 2,
        feature: 'STORAGE',
        amount: 10,
        usageTime: Date.now() - 1 * 24 * 60 * 60 * 1000,
        createdAt: Date.now() - 1 * 24 * 60 * 60 * 1000
      }
    ];

    // Filter by date range if provided
    let filteredItems = mockUsageLogList;
    
    if (startDate && endDate) {
      filteredItems = mockUsageLogList.filter(
        item => item.usageTime >= startDate && item.usageTime <= endDate
      );
    } else if (startDate) {
      filteredItems = mockUsageLogList.filter(item => item.usageTime >= startDate);
    } else if (endDate) {
      filteredItems = mockUsageLogList.filter(item => item.usageTime <= endDate);
    }

    return {
      items: filteredItems,
      meta: {
        totalItems: filteredItems.length,
        itemCount: filteredItems.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(filteredItems.length / limit),
        currentPage: page
      }
    };
  }
}

// Mock controller
class MockSubscriptionUserController {
  constructor(private readonly subscriptionUserService: MockSubscriptionUserService) {}

  async findAll(req: { user: JwtPayload }, filterDto: { status?: SubscriptionStatus; page?: number; limit?: number }) {
    const { status, page = 1, limit = 10 } = filterDto;
    const userId = req.user.id;
    const result = await this.subscriptionUserService.findSubscriptions(userId, status || null, page, limit);
    
    const responseData = {
      items: result.items.map(item => {
        const dto = { ...item };
        if ((item as any).planInfo) {
          (dto as any).planInfo = (item as any).planInfo;
        }
        if ((item as any).pricingInfo) {
          (dto as any).pricingInfo = (item as any).pricingInfo;
        }
        return dto;
      }),
      meta: result.meta
    };
    
    return {
      code: 200,
      message: 'Success',
      result: responseData
    } as ApiResponse;
  }

  async findOne(id: number) {
    const { subscription, planInfo, pricingInfo } = await this.subscriptionUserService.findSubscriptionWithDetails(id);
    
    return {
      code: 200,
      message: 'Success',
      result: { ...subscription, planInfo, pricingInfo }
    } as ApiResponse;
  }

  async create(req: { user: JwtPayload }, createDto: { planPricingId: number; autoRenew?: boolean }) {
    const userId = req.user.id;
    const subscription = await this.subscriptionUserService.createSubscription(
      userId,
      createDto.planPricingId,
      createDto.autoRenew
    );
    
    return {
      code: 201,
      message: 'Subscription created successfully',
      result: subscription
    } as ApiResponse;
  }

  async cancel(id: number) {
    const subscription = await this.subscriptionUserService.cancelSubscription(id);
    
    const responseData = {
      id: subscription.id,
      status: subscription.status,
      updatedAt: subscription.updatedAt
    };
    
    return {
      code: 200,
      message: 'Subscription cancelled successfully',
      result: responseData
    } as ApiResponse;
  }

  async updateAutoRenew(id: number, updateDto: { autoRenew: boolean }) {
    const subscription = await this.subscriptionUserService.updateAutoRenew(id, updateDto.autoRenew);
    
    const responseData = {
      id: subscription.id,
      autoRenew: subscription.autoRenew,
      updatedAt: subscription.updatedAt
    };
    
    return {
      code: 200,
      message: 'Auto-renew setting updated successfully',
      result: responseData
    } as ApiResponse;
  }

  async changePlan(id: number, changeDto: { newPlanPricingId: number; effectiveImmediately?: boolean }) {
    const subscription = await this.subscriptionUserService.changePlan(
      id,
      changeDto.newPlanPricingId,
      changeDto.effectiveImmediately
    );
    
    const { planInfo, pricingInfo } = await this.subscriptionUserService.findSubscriptionWithDetails(id);
    
    return {
      code: 200,
      message: 'Subscription plan changed successfully',
      result: { ...subscription, planInfo, pricingInfo }
    } as ApiResponse;
  }

  async findUsageLogs(subscriptionId: number, filterDto: { startDate?: number; endDate?: number; page?: number; limit?: number }) {
    const { startDate, endDate, page = 1, limit = 10 } = filterDto;
    const result = await this.subscriptionUserService.findUsageLogs(
      subscriptionId,
      startDate || null,
      endDate || null,
      page,
      limit
    );
    
    const responseData = {
      items: result.items.map(item => ({ ...item })),
      meta: result.meta
    };
    
    return {
      code: 200,
      message: 'Success',
      result: responseData
    } as ApiResponse;
  }
}

describe('SubscriptionUserController', () => {
  let controller: MockSubscriptionUserController;
  let service: MockSubscriptionUserService;

  const mockRequest = {
    user: {
      id: 10,
      email: '<EMAIL>',
      role: 'USER'
    } as JwtPayload
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MockSubscriptionUserService,
        {
          provide: MockSubscriptionUserController,
          useFactory: (service: MockSubscriptionUserService) => new MockSubscriptionUserController(service),
          inject: [MockSubscriptionUserService],
        },
      ],
    }).compile();

    controller = module.get<MockSubscriptionUserController>(MockSubscriptionUserController);
    service = module.get<MockSubscriptionUserService>(MockSubscriptionUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all subscriptions for the user with pagination', async () => {
      const filterDto = { page: 1, limit: 10 };
      const result = await controller.findAll(mockRequest, filterDto);
      
      expect(result.code).toBe(200);
      expect(result.message).toBe('Success');
      expect(result.result).toHaveProperty('items');
      expect(result.result).toHaveProperty('meta');
      expect(result.result.items.length).toBeGreaterThan(0);
    });

    it('should filter subscriptions by status when provided', async () => {
      const filterDto = { 
        page: 1, 
        limit: 10,
        status: SubscriptionStatus.ACTIVE
      };
      const result = await controller.findAll(mockRequest, filterDto);
      
      expect(result.code).toBe(200);
      expect(result.result.items.length).toBeGreaterThan(0);
      expect(result.result.items.every(item => item.status === SubscriptionStatus.ACTIVE)).toBe(true);
    });
  });

  describe('findOne', () => {
    it('should return a subscription with details by id', async () => {
      const result = await controller.findOne(1);
      
      expect(result.code).toBe(200);
      expect(result.result.id).toBe(1);
      expect(result.result).toHaveProperty('planInfo');
      expect(result.result).toHaveProperty('pricingInfo');
    });

    it('should throw NotFoundException if subscription is not found', async () => {
      await expect(controller.findOne(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('create', () => {
    it('should create a new subscription', async () => {
      const createDto = {
        planPricingId: 1,
        autoRenew: true
      };
      const result = await controller.create(mockRequest, createDto);
      
      expect(result.code).toBe(201);
      expect(result.message).toBe('Subscription created successfully');
      expect(result.result.id).toBe(1);
      expect(result.result.planPricingId).toBe(1);
    });

    it('should throw NotFoundException if plan pricing is not found', async () => {
      const createDto = {
        planPricingId: 999,
        autoRenew: true
      };
      await expect(controller.create(mockRequest, createDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('cancel', () => {
    it('should cancel a subscription', async () => {
      const result = await controller.cancel(1);
      
      expect(result.code).toBe(200);
      expect(result.message).toBe('Subscription cancelled successfully');
      expect(result.result).toHaveProperty('id');
      expect(result.result).toHaveProperty('status');
      expect(result.result.status).toBe(SubscriptionStatus.CANCELLED);
    });

    it('should throw NotFoundException if subscription is not found', async () => {
      await expect(controller.cancel(999)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if subscription is not active', async () => {
      await expect(controller.cancel(888)).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateAutoRenew', () => {
    it('should update auto-renew setting', async () => {
      const updateDto = {
        autoRenew: false
      };
      const result = await controller.updateAutoRenew(1, updateDto);
      
      expect(result.code).toBe(200);
      expect(result.message).toBe('Auto-renew setting updated successfully');
      expect(result.result).toHaveProperty('id');
      expect(result.result).toHaveProperty('autoRenew');
    });

    it('should throw NotFoundException if subscription is not found', async () => {
      const updateDto = {
        autoRenew: false
      };
      await expect(controller.updateAutoRenew(999, updateDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('changePlan', () => {
    it('should change subscription plan', async () => {
      const changeDto = {
        newPlanPricingId: 2,
        effectiveImmediately: true
      };
      const result = await controller.changePlan(1, changeDto);
      
      expect(result.code).toBe(200);
      expect(result.message).toBe('Subscription plan changed successfully');
      expect(result.result.id).toBe(1);
      expect(result.result.planPricingId).toBe(2);
      expect(result.result).toHaveProperty('planInfo');
      expect(result.result).toHaveProperty('pricingInfo');
    });

    it('should throw NotFoundException if subscription is not found', async () => {
      const changeDto = {
        newPlanPricingId: 2,
        effectiveImmediately: true
      };
      await expect(controller.changePlan(999, changeDto)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if subscription is not active', async () => {
      const changeDto = {
        newPlanPricingId: 2,
        effectiveImmediately: true
      };
      await expect(controller.changePlan(888, changeDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findUsageLogs', () => {
    it('should return usage logs for a subscription with pagination', async () => {
      const filterDto = { page: 1, limit: 10 };
      const result = await controller.findUsageLogs(1, filterDto);
      
      expect(result.code).toBe(200);
      expect(result.result).toHaveProperty('items');
      expect(result.result).toHaveProperty('meta');
      expect(result.result.items.length).toBeGreaterThan(0);
      expect(result.result.items[0]).toHaveProperty('subscriptionId');
      expect(result.result.items[0].subscriptionId).toBe(1);
    });

    it('should filter usage logs by date range when provided', async () => {
      const startDate = Date.now() - 7 * 24 * 60 * 60 * 1000;
      const endDate = Date.now();
      const filterDto = { 
        page: 1, 
        limit: 10,
        startDate,
        endDate
      };
      const result = await controller.findUsageLogs(1, filterDto);
      
      expect(result.code).toBe(200);
      expect(result.result.items.length).toBeGreaterThan(0);
    });

    it('should throw NotFoundException if subscription is not found', async () => {
      const filterDto = { page: 1, limit: 10 };
      await expect(controller.findUsageLogs(999, filterDto)).rejects.toThrow(NotFoundException);
    });
  });
});
