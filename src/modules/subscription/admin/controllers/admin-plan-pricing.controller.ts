import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AdminPlanPricingService } from '../services';
import { AdminPlanPricingFilterDto, CreatePlanPricingDto, UpdatePlanPricingDto } from '../dto';
import { PlanPricing } from '@modules/subscription/entities';

@ApiTags(SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_PLAN_PRICING)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/subscription/plan-pricings')
export class AdminPlanPricingController {
  constructor(private readonly adminPlanPricingService: AdminPlanPricingService) {}

  /**
   * Lấy danh sách tùy chọn giá
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tùy chọn giá' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách tùy chọn giá thành công'
  })
  async findPlanPricings(
    @Query() filterDto: AdminPlanPricingFilterDto
  ): Promise<ApiResponseDto<PaginatedResult<PlanPricing>>> {
    const planPricings = await this.adminPlanPricingService.findPlanPricings(filterDto);
    return ApiResponseDto.success(planPricings, 'Lấy danh sách tùy chọn giá thành công');
  }

  /**
   * Lấy thông tin chi tiết tùy chọn giá
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tùy chọn giá' })
  @ApiParam({ name: 'id', description: 'ID của tùy chọn giá' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết tùy chọn giá thành công'
  })
  async findPlanPricingById(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<PlanPricing>> {
    const planPricing = await this.adminPlanPricingService.findPlanPricingById(id);
    return ApiResponseDto.success(planPricing, 'Lấy thông tin chi tiết tùy chọn giá thành công');
  }

  /**
   * Lấy danh sách vai trò được phép sử dụng tùy chọn giá
   */
  @Get(':id/roles')
  @ApiOperation({ summary: 'Lấy danh sách vai trò được phép sử dụng tùy chọn giá' })
  @ApiParam({ name: 'id', description: 'ID của tùy chọn giá' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách vai trò thành công'
  })
  async findRolesByPlanPricingId(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<number[]>> {
    const roles = await this.adminPlanPricingService.findRolesByPlanPricingId(id);
    return ApiResponseDto.success(roles, 'Lấy danh sách vai trò thành công');
  }

  /**
   * Tạo tùy chọn giá mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo tùy chọn giá mới' })
  @ApiBody({ type: CreatePlanPricingDto })
  @ApiResponse({
    status: 201,
    description: 'Tạo tùy chọn giá mới thành công'
  })
  async createPlanPricing(
    @Body() createPlanPricingDto: CreatePlanPricingDto
  ): Promise<ApiResponseDto<PlanPricing>> {
    const planPricing = await this.adminPlanPricingService.createPlanPricing(createPlanPricingDto);
    return ApiResponseDto.success(planPricing, 'Tạo tùy chọn giá mới thành công');
  }

  /**
   * Cập nhật thông tin tùy chọn giá
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin tùy chọn giá' })
  @ApiParam({ name: 'id', description: 'ID của tùy chọn giá' })
  @ApiBody({ type: UpdatePlanPricingDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thông tin tùy chọn giá thành công'
  })
  async updatePlanPricing(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePlanPricingDto: UpdatePlanPricingDto
  ): Promise<ApiResponseDto<PlanPricing>> {
    const planPricing = await this.adminPlanPricingService.updatePlanPricing(id, updatePlanPricingDto);
    return ApiResponseDto.success(planPricing, 'Cập nhật thông tin tùy chọn giá thành công');
  }

  /**
   * Xóa tùy chọn giá
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tùy chọn giá' })
  @ApiParam({ name: 'id', description: 'ID của tùy chọn giá' })
  @ApiResponse({
    status: 200,
    description: 'Xóa tùy chọn giá thành công'
  })
  async deletePlanPricing(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<null>> {
    await this.adminPlanPricingService.deletePlanPricing(id);
    return ApiResponseDto.success(null, 'Xóa tùy chọn giá thành công');
  }
}
