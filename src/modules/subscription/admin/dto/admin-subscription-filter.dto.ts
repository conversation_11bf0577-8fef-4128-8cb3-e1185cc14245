import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { SubscriptionStatus } from '@modules/subscription/enums';
import { QueryDto } from '@common/dto';

export class AdminSubscriptionFilterDto extends QueryDto {
  @ApiProperty({
    description: 'ID của người dùng',
    required: false,
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiProperty({
    description: 'ID của gói dịch vụ',
    required: false,
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  planId?: number;

  @ApiProperty({
    description: 'Trạng thái đăng ký',
    enum: SubscriptionStatus,
    required: false,
    example: SubscriptionStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(SubscriptionStatus)
  status?: SubscriptionStatus;
}
