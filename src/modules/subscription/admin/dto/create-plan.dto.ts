import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';
import { PackageType } from '@modules/subscription/enums';

export class CreatePlanDto {
  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Premium Plan',
    maxLength: 100
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Mô tả gói dịch vụ',
    example: '<PERSON><PERSON>i dịch vụ cao cấp với nhiều tính năng',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại gói dịch vụ',
    enum: PackageType,
    example: PackageType.TIME_ONLY
  })
  @IsNotEmpty()
  @IsEnum(PackageType)
  packageType: PackageType;
}
