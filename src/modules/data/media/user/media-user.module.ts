import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaUserController } from './controllers/media-user.controller';
import { MediaUserService } from './services/media-user.service';
import { Media } from '../entities/media.entity';
import { MediaRepository } from '../repositories';
import { AuthModule } from '@modules/auth/auth.module';
import { AgentMedia } from '@modules/agent/entities';
import { DataSource } from 'typeorm';
import { AgentMediaRepository } from '@modules/agent/repositories/agent-media.repository';
import { MediaValidationHelper } from '../helpers/validation.helper';

@Module({
  imports: [TypeOrmModule.forFeature([Media,AgentMedia]), AuthModule],
  controllers: [MediaUserController],
  providers: [
    MediaUserService,
    {
      provide: MediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new MediaRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: AgentMediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new AgentMediaRepository(dataSource);
      },
      inject: [DataSource],
    },
    MediaValidationHelper,
  ],
  exports: [MediaUserService],
})
export class MediaUserModule {}
