import { Test, TestingModule } from '@nestjs/testing';
import { MediaUserController } from '../../../../user/controllers/media-user.controller';
import { MediaUserService } from '../../../../user/services/media-user.service';
import { MediaRepository } from '../../../../repositories';
import { JwtUserGuard } from '@/modules/auth/guards';
import { QueryDto } from '@common/dto';
import { MediaDto } from '../../../../dto/media.dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { Media } from '../../../../entities/media.entity';
import { AppException, ErrorCode } from '@/common/exceptions';

describe('MediaUserController', () => {
  let controller: MediaUserController;
  let service: jest.Mocked<MediaUserService>;
  let repository: jest.Mocked<MediaRepository>;

  // Mock data
  const mockMedia: Media = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Test Media',
    description: 'Test Description',
    size: 1024,
    tags: ['test', 'media'],
    storageKey: 'media/test/123e4567-e89b-12d3-a456-426614174000.jpg',
    ownedBy: 1,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
    nameEmbedding: [],
    descriptionEmbedding: [],
    status: 'DRAFT' as any,
  };

  const mockMediaList: Media[] = [mockMedia];

  const mockPaginatedResult: PaginatedResult<Media> = {
    items: mockMediaList,
    meta: {
      totalItems: mockMediaList.length,
      itemCount: mockMediaList.length,
      itemsPerPage: mockMediaList.length,
      totalPages: 1,
      currentPage: 1
    }
  };

  const mockUser = {
    sub: 1, // userId
    email: '<EMAIL>',
    roles: ['user'],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MediaUserController],
      providers: [
        {
          provide: MediaUserService,
          useValue: {
            findById: jest.fn(),
            findAllByUser: jest.fn(),
            deleteManyByUser: jest.fn(),
            createPresignedUrlsFromMediaList: jest.fn(),
          },
        },
        {
          provide: MediaRepository,
          useValue: {
            findAllUserMedia: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(JwtUserGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<MediaUserController>(MediaUserController);
    service = module.get(MediaUserService) as jest.Mocked<MediaUserService>;
    repository = module.get(MediaRepository) as jest.Mocked<MediaRepository>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findMyMedia', () => {
    it('should return paginated media list for current user', async () => {
      // Arrange
      const query: QueryDto = {
        page: 1,
        limit: 10,
      };
      const apiResponse = new ApiResponseDto<PaginatedResult<Media>>(mockPaginatedResult);
      service.findAllByUser.mockResolvedValue(apiResponse);

      // Act
      const result = await controller.findMyMedia(mockUser, query);

      // Assert
      expect(service.findAllByUser).toHaveBeenCalledWith(mockUser.sub, query);
      expect(result).toBe(apiResponse);
    });
  });

  describe('findOne', () => {
    it('should return media by id for current user', async () => {
      // Arrange
      const mediaId = '123e4567-e89b-12d3-a456-426614174000';
      const apiResponse = new ApiResponseDto<Media>(mockMedia);
      service.findById.mockResolvedValue(apiResponse);

      // Act
      const result = await controller.findOne(mockUser, mediaId);

      // Assert
      expect(service.findById).toHaveBeenCalledWith(mediaId, mockUser.sub);
      expect(result).toBe(apiResponse);
    });
  });

  describe('deleteManyMyMedia', () => {
    it('should delete multiple media files for current user', async () => {
      // Arrange
      const keys = ['media/key1.jpg', 'media/key2.jpg'];
      const deleteResult = {
        deleted: keys,
        errors: [],
      };
      const apiResponse = new ApiResponseDto(deleteResult);
      service.deleteManyByUser.mockResolvedValue(apiResponse);

      // Act
      const result = await controller.deleteManyMyMedia(mockUser, keys);

      // Assert
      expect(service.deleteManyByUser).toHaveBeenCalledWith(mockUser.sub, keys);
      expect(result).toBe(apiResponse);
    });
  });

  describe('createPresignedUrlsFromMediaList', () => {
    it('should create presigned URLs for media list', async () => {
      // Arrange
      const mediaList: MediaDto[] = [
        {
          name: 'test-image.jpg',
          description: 'Test image',
          size: 1024,
          tags: ['test', 'image'],
          type: 'image/jpeg',
          storageKey: '',
          ownedBy: mockUser.sub,
        },
      ];
      const presignedUrls = ['https://presigned-url.example.com'];
      const apiResponse = new ApiResponseDto<string[]>(presignedUrls);
      service.createPresignedUrlsFromMediaList.mockResolvedValue(apiResponse);

      // Act
      const result = await controller.createPresignedUrlsFromMediaList(mockUser, mediaList);

      // Assert
      expect(service.createPresignedUrlsFromMediaList).toHaveBeenCalledWith(mediaList, mockUser.sub);
      expect(result).toBe(apiResponse);
    });

    it('should handle exceptions when creating presigned URLs', async () => {
      // Arrange
      const mediaList: MediaDto[] = [
        {
          name: 'test-image.jpg',
          description: 'Test image',
          size: 1024,
          tags: ['test', 'image'],
          type: 'image/jpeg',
          storageKey: '',
          ownedBy: mockUser.sub,
        },
      ];
      service.createPresignedUrlsFromMediaList.mockRejectedValue(new Error('Test error'));

      // Act & Assert
      await expect(controller.createPresignedUrlsFromMediaList(mockUser, mediaList)).rejects.toThrow(AppException);
      expect(service.createPresignedUrlsFromMediaList).toHaveBeenCalledWith(mediaList, mockUser.sub);
    });
  });
});
