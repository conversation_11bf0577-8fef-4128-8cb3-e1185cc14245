import { ApiProperty } from '@nestjs/swagger';
import { IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CopyFileDto } from './copy-file.dto';

export class CopyFilesDto {
  @ApiProperty({ description: 'List of copy operations', type: [CopyFileDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CopyFileDto)
  copyOperations: CopyFileDto[];
}
