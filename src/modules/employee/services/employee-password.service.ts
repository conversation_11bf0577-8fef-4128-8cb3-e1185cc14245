import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { EmployeeRepository } from '../repositories/employee.repository';

/**
 * Service xử lý các thao tác liên quan đến mật khẩu của nhân viên
 */
@Injectable()
export class EmployeePasswordService {
  private readonly logger = new Logger(EmployeePasswordService.name);

  constructor(private readonly employeeRepository: EmployeeRepository) {}

  /**
   * Kiểm tra độ mạnh của mật khẩu
   * @param password Mật khẩu cần kiểm tra
   * @returns Thông tin về độ mạnh của mật khẩu
   */
  checkPasswordStrength(password: string): { score: number; feedback: string } {
    let score = 0;
    const feedback: string[] = [];

    // Kiểm tra độ dài
    if (password.length < 8) {
      feedback.push('<PERSON><PERSON>t khẩu quá ngắn (tối thiểu 8 ký tự)');
    } else {
      score += 1;
    }

    // Kiểm tra chữ hoa
    if (!/[A-Z]/.test(password)) {
      feedback.push('Mật khẩu nên chứa ít nhất 1 chữ hoa');
    } else {
      score += 1;
    }

    // Kiểm tra chữ thường
    if (!/[a-z]/.test(password)) {
      feedback.push('Mật khẩu nên chứa ít nhất 1 chữ thường');
    } else {
      score += 1;
    }

    // Kiểm tra số
    if (!/[0-9]/.test(password)) {
      feedback.push('Mật khẩu nên chứa ít nhất 1 số');
    } else {
      score += 1;
    }

    // Kiểm tra ký tự đặc biệt
    if (!/[^A-Za-z0-9]/.test(password)) {
      feedback.push('Mật khẩu nên chứa ít nhất 1 ký tự đặc biệt');
    } else {
      score += 1;
    }

    // Đánh giá độ mạnh
    let strengthFeedback = '';
    if (score < 2) {
      strengthFeedback = 'Mật khẩu yếu';
    } else if (score < 4) {
      strengthFeedback = 'Mật khẩu trung bình';
    } else {
      strengthFeedback = 'Mật khẩu mạnh';
    }

    return {
      score,
      feedback: feedback.length > 0 ? `${strengthFeedback}. ${feedback.join('. ')}` : strengthFeedback,
    };
  }

  /**
   * Kiểm tra và validate độ mạnh của mật khẩu
   * @param password Mật khẩu cần kiểm tra
   * @throws BadRequestException nếu mật khẩu không đủ mạnh
   */
  validatePasswordStrength(password: string): void {
    const { score, feedback } = this.checkPasswordStrength(password);
    if (score < 3) {
      throw new BadRequestException(`Mật khẩu không đủ mạnh. ${feedback}`);
    }
  }

  /**
   * Đổi mật khẩu cho nhân viên
   * @param employeeId ID của nhân viên
   * @param newPassword Mật khẩu mới
   * @returns Thông báo kết quả
   */
  async setPassword(employeeId: number, newPassword: string): Promise<{ message: string }> {
    // Kiểm tra độ mạnh của mật khẩu
    this.validatePasswordStrength(newPassword);

    // Đổi mật khẩu
    await this.employeeRepository.changePassword(employeeId, newPassword);

    this.logger.log(`Đã đổi mật khẩu cho nhân viên có ID ${employeeId}`);
    return { message: 'Đổi mật khẩu thành công' };
  }

  /**
   * Mã hóa mật khẩu
   * @param password Mật khẩu cần mã hóa
   * @returns Mật khẩu đã mã hóa
   */
  async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt();
    return bcrypt.hash(password, salt);
  }

  /**
   * So sánh mật khẩu
   * @param plainPassword Mật khẩu gốc
   * @param hashedPassword Mật khẩu đã mã hóa
   * @returns true nếu mật khẩu khớp, ngược lại false
   */
  async comparePasswords(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}
