import { ApiProperty } from '@nestjs/swagger';
import { Blog } from '@modules/blog/entities';
import { AuthorTypeEnum, BlogStatusEnum } from '../enums';
import { Expose, Type } from 'class-transformer';

export class AuthorDto {
  @Expose()
  @ApiProperty({
    description: 'ID của tác giả',
    example: 1,
    nullable: true,
  })
  id: number | null;

  @Expose()
  @ApiProperty({
    description: 'Tên tác giả',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Loại tác giả',
    example: AuthorTypeEnum.USER,
    enum: AuthorTypeEnum,
  })
  type: AuthorTypeEnum;

  @Expose()
  @ApiProperty({
    description: 'Avatar của tác giả',
    example: 'https://cdn.example.com/avatars/user10.jpg',
    nullable: true,
  })
  avatar: string;
}

export class BlogResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của bài viết',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Tiêu đề bài viết',
    example: 'Tiêu đề bài viết',
  })
  title: string;

  @Expose()
  @ApiProperty({
    description: 'URL file content trên CDN. Sẽ là null nếu người dùng chưa mua bài viết.',
    example: 'URL file content trên CDN',
    nullable: true,
  })
  content: string | null;

  @Expose()
  @ApiProperty({
    description: 'URL tải lên nội dung',
    example: 'https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...',
    required: false,
  })
  contentUploadUrl?: string;

  @Expose()
  @ApiProperty({
    description: 'URL tải lên thumbnail',
    example: 'https://cdn-storage.example.com/temp/uploads/123456789/thumbnail.jpg?signature=def456...',
    required: false,
  })
  thumbnailUploadUrl?: string;

  @Expose()
  @ApiProperty({
    description: 'Số point',
    example: 100,
  })
  point: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượt xem',
    example: 150,
  })
  viewCount: number;

  @Expose()
  @ApiProperty({
    description: 'URL thumbnail',
    example: 'URL thumbnail',
  })
  thumbnailUrl: string;

  @Expose()
  @ApiProperty({
    description: 'Tags',
    example: ['tag1', 'tag2'],
    type: [String],
  })
  tags: string[];

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1632474086123,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1632474086123,
  })
  updatedAt: number;

  @Expose()
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
    nullable: true,
  })
  userId?: number | null;

  @Expose()
  @ApiProperty({
    description: 'ID của nhân viên',
    example: 1,
    nullable: true,
  })
  employeeId?: number | null;

  @Expose()
  @ApiProperty({
    description: 'Loại tác giả',
    example: AuthorTypeEnum.USER,
    enum: AuthorTypeEnum,
    nullable: true,
  })
  authorType?: AuthorTypeEnum;

  // User object for internal use
  user?: any;

  @Expose()
  @Type(() => AuthorDto)
  @ApiProperty({
    description: 'Thông tin tác giả',
    type: AuthorDto,
  })
  author: AuthorDto;

  @Expose()
  @ApiProperty({
    description: 'ID nhân viên kiểm duyệt',
    example: null,
    nullable: true,
  })
  employeeModerator: number | null;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái bài viết',
    example: BlogStatusEnum.APPROVED,
    enum: BlogStatusEnum,
  })
  status: BlogStatusEnum;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái hiển thị',
    example: true,
  })
  enable: boolean;

  @Expose()
  @ApiProperty({
    description: 'Số lượt thích',
    example: 45,
  })
  like: number;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái đã mua của người dùng hiện tại',
    example: true,
    required: false,
  })
  isPurchased?: boolean;

  static fromEntity(entity: Blog, user?: any, isPurchased: boolean = true): BlogResponseDto {
    const response = new BlogResponseDto();
    response.id = entity.id;
    response.title = entity.title;
    response.content = isPurchased ? entity.content : null;
    response.isPurchased = isPurchased;
    response.point = entity.point;
    response.viewCount = entity.viewCount;
    response.thumbnailUrl = entity.thumbnailUrl;
    response.tags = entity.tags;
    response.createdAt = entity.createdAt;
    response.updatedAt = entity.updatedAt;
    response.userId = entity.userId;
    response.employeeId = entity.employeeId;
    response.authorType = entity.authorType;
    response.status = entity.status;
    response.enable = entity.enable;
    response.like = entity.like;
    response.employeeModerator = entity.employeeModerator;

    // Construct author information
    response.author = {
      id:
        entity.authorType === AuthorTypeEnum.USER
          ? (entity.userId ?? null)
          : (entity.employeeId ?? null),
      name: user?.fullName || null,
      type: entity.authorType || AuthorTypeEnum.SYSTEM,
      avatar: user?.avatar || null,
    };

    return response;
  }
}

export class PaginatedBlogResponseDto {
  @Expose()
  @Type(() => BlogResponseDto)
  @ApiProperty({
    description: 'Danh sách bài viết',
    type: [BlogResponseDto],
  })
  content: BlogResponseDto[];

  @Expose()
  @ApiProperty({
    description: 'Tổng số bản ghi',
    example: 100,
  })
  totalItems: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng bản ghi trên trang hiện tại',
    example: 10,
  })
  itemCount: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
  })
  itemsPerPage: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng số trang',
    example: 10,
  })
  totalPages: number;

  @Expose()
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  currentPage: number;
}
