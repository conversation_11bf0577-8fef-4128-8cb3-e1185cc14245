import { ApiProperty } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { AuthorTypeEnum, BlogStatusEnum } from '../enums';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

export class GetBlogsDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: BlogStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(BlogStatusEnum)
  status?: BlogStatusEnum;

  @ApiProperty({
    description: 'Lọc theo loại tác giả',
    enum: AuthorTypeEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(AuthorTypeEnum)
  @Transform(({ value, obj }) => {
    // Hỗ trợ cả camelCase và snake_case
    return value || obj.author_type;
  })
  authorType?: AuthorTypeEnum;

  // Đã bỏ lọc theo tags

  // Hỗ trợ tương thích ngư<PERSON> với các tham số cũ
  @Transform(({ value, obj }) => {
    // Nếu có sort, gán giá trị cho sortBy
    if (obj.sort && !obj.sortBy) {
      obj.sortBy = obj.sort;
    }
    // Nếu có order, gán giá trị cho sortDirection
    if (obj.order && !obj.sortDirection) {
      obj.sortDirection = obj.order;
    }
    return value;
  })
  sort?: string;

  @Transform(({ value }) => value)
  order?: 'ASC' | 'DESC';

  // Thêm @Type decorator để đảm bảo chuyển đổi đúng kiểu dữ liệu
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  @Type(() => String)
  override sortDirection: SortDirection = SortDirection.DESC;

  // Thêm @Type decorator để đảm bảo chuyển đổi đúng kiểu dữ liệu cho sortBy
  @ApiProperty({
    description: 'Trường sắp xếp',
    default: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  override sortBy: string = 'createdAt';
}