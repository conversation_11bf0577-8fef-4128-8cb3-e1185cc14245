import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';
import { BlogOwnershipEnum } from '../enums/blog-ownership.enum';
import { GetBlogsDto } from './get-blogs.dto';

/**
 * DTO mở rộng từ GetBlogsDto để bao gồm thêm các tùy chọn lọc theo loại sở hữu
 */
export class GetBlogsExtendedDto extends GetBlogsDto {
  @ApiProperty({
    description: 'Lọc theo loại sở hữu bài viết',
    enum: BlogOwnershipEnum,
    required: false,
    example: BlogOwnershipEnum.CREATED,
  })
  @IsOptional()
  @IsEnum(BlogOwnershipEnum)
  @Type(() => String)
  @Transform(({ value }) => value)
  ownership_type?: BlogOwnershipEnum;
}
