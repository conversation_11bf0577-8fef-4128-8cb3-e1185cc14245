import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { BlogOwnershipEnum } from '../enums/blog-ownership.enum';
import { GetBlogsExtendedDto } from './get-blogs-extended.dto';

/**
 * DTO thống nhất cho việc lấy danh sách bài viết với đầy đủ các tùy chọn lọc
 * Mở rộng từ GetBlogsExtendedDto để bao gồm thêm các tùy chọn lọc mới
 */
export class GetBlogsUnifiedDto extends GetBlogsExtendedDto {
  // Không cần định nghĩa lại ownership_type vì đã được định nghĩa trong GetBlogsExtendedDto
}
