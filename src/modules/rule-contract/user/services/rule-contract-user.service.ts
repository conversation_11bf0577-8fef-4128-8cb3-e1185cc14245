import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { RuleContractRepository } from '../../repositories';
import {
  RuleContractQueryDto,
  RuleContractResponseDto,
  RegisterRuleContractDto,
  RuleContractStatusResponseDto,
  CreateIndividualRuleContractDto,
  RuleContractExtendedResponseDto
} from '../dto';
import { RULE_CONTRACT_ERROR_CODES } from '../../errors';
import { Transactional } from 'typeorm-transactional';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { ContractStatusEnum, ContractTypeEnum } from '../../entities/rule-contract.entity';

import { RuleContractStateService } from '../../state-machine/rule-contract-state.service';
import {
  CreateEventData,
  IndividualContractData,
  mapStateToStatus,
  RuleContractState
} from '../../state-machine/rule-contract.types';

/**
 * Service xử lý logic nghiệp vụ cho hợp đồng nguyên tắc (user)
 */
@Injectable()
export class RuleContractUserService {
  private readonly logger = new Logger(RuleContractUserService.name);

  constructor(
    private readonly ruleContractRepository: RuleContractRepository,
    private readonly cdnService: CdnService,
    private readonly ruleContractStateService: RuleContractStateService,
  ) {}

  /**
   * Đăng ký loại hợp đồng nguyên tắc
   * @param userId ID của người dùng
   * @param dto Thông tin đăng ký
   * @returns Trạng thái hợp đồng
   */
  @Transactional()
  async registerTypeRuleContract(
    userId: number,
    dto: RegisterRuleContractDto,
  ): Promise<RuleContractStatusResponseDto> {
    try {
      // Sử dụng state machine để tạo hợp đồng
      const createData: CreateEventData = {
        userId,
        contractType: dto.type,
      };

      // Tạo hợp đồng mới thông qua state machine
      await this.ruleContractStateService.createContract(userId, createData);

      // Trả về trạng thái hợp đồng
      return {
        status: mapStateToStatus(RuleContractState.DRAFT),
        type: dto.type,
      };
    } catch (error) {
      this.logger.error(
        `Error registering rule contract for user ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi đăng ký hợp đồng nguyên tắc',
      );
    }
  }

  /**
   * Lấy danh sách hợp đồng nguyên tắc của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  @Transactional()
  async getContracts(
    userId: number,
    queryDto: RuleContractQueryDto,
  ): Promise<PaginatedResult<RuleContractResponseDto>> {
    try {
      // Lấy danh sách hợp đồng với phân trang
      const { items, meta } = await this.ruleContractRepository.findWithPaginationForUser(
        userId,
        queryDto,
      );

      // Chuyển đổi dữ liệu sang DTO
      const contractDtos = await Promise.all(items.map(contract => this.mapContractToDto(contract)));

      return {
        items: contractDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting contracts for user ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách hợp đồng nguyên tắc',
      );
    }
  }

  /**
   * Lấy chi tiết hợp đồng nguyên tắc của người dùng
   * @param userId ID của người dùng
   * @param id ID của hợp đồng
   * @returns Thông tin chi tiết hợp đồng
   */
  @Transactional()
  async getContractById(userId: number, id: number): Promise<RuleContractResponseDto> {
    try {
      // Lấy thông tin chi tiết hợp đồng
      const contract = await this.ruleContractRepository.findById(id);

      if (!contract) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          `Không tìm thấy hợp đồng nguyên tắc với ID ${id}`,
        );
      }

      // Kiểm tra quyền truy cập
      if (contract.userId !== userId) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.UNAUTHORIZED_ACCESS,
          'Bạn không có quyền truy cập hợp đồng này',
        );
      }

      // Xử lý dữ liệu trả về
      return this.mapContractToDto(contract);
    } catch (error) {
      this.logger.error(
        `Error getting contract for user ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin chi tiết hợp đồng nguyên tắc',
      );
    }
  }
  /**
   * Tạo hợp đồng nguyên tắc cho cá nhân
   * @param userId ID của người dùng
   * @param dto Thông tin hợp đồng
   * @returns Thông tin hợp đồng đã tạo
   */
  @Transactional()
  async createIndividualRuleContract(
    userId: number,
    dto: CreateIndividualRuleContractDto,
  ): Promise<RuleContractExtendedResponseDto> {
    try {
      this.logger.log(`Tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}`);

      // Sử dụng state machine để tạo hợp đồng cá nhân
      const individualData: IndividualContractData = {
        name: dto.name,
        address: dto.address,
        phone: dto.phone,
        dateOfBirth: dto.dateOfBirth,
        cccd: dto.cccd,
        issuePlace: dto.issuePlace,
        issueDate: dto.issueDate,
        taxCode: dto.taxCode,
      };

      const result = await this.ruleContractStateService.createIndividualContract(userId, individualData);

      // Trả về thông tin hợp đồng
      return {
        status: ContractStatusEnum.DRAFT,
        type: ContractTypeEnum.INDIVIDUAL,
        contractBase64: result.contractBase64,
        contractUrl: result.contractUrl || '',
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân',
      );
    }
  }

  /**
   * Lấy trạng thái hợp đồng nguyên tắc mới nhất của người dùng
   * @param userId ID của người dùng
   * @returns Trạng thái hợp đồng mới nhất
   */
  @Transactional()
  async getLatestContractStatus(userId: number): Promise<RuleContractStatusResponseDto | null> {
    try {
      // Lấy hợp đồng mới nhất của người dùng
      const latestContract = await this.ruleContractRepository.findLatestByUserId(userId);

      // Nếu không tìm thấy hợp đồng, trả về null
      if (!latestContract) {
        return null;
      }

      // Trả về trạng thái hợp đồng
      return {
        status: latestContract.status,
        type: latestContract.type,
      };
    } catch (error) {
      this.logger.error(
        `Error getting latest contract status for user ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED,
        'Lỗi khi lấy trạng thái hợp đồng nguyên tắc mới nhất',
      );
    }
  }

  /**
   * Helper method để chuyển đổi contract entity sang DTO
   * @param contract Contract entity
   * @returns Contract DTO
   */
  private mapContractToDto(contract: any): RuleContractResponseDto {
    // Tạo URL có chữ ký cho file hợp đồng
    let contractUrl = '';
    if (contract.contractUrlPdf) {
      const generatedUrl = this.cdnService.generateUrlView(
        contract.contractUrlPdf as string,
        TimeIntervalEnum.ONE_HOUR,
      );
      if (generatedUrl) {
        contractUrl = generatedUrl;
      }
    }

    return {
      id: contract.id,
      contractCode: `HD-${contract.id}`,
      status: contract.status,
      type: contract.type,
      contractUrl,
      createdAt: contract.createdAt,
      userSignatureAt: contract.userSignatureAt,
      adminSignatureAt: contract.adminSignatureAt,
    };
  }
}