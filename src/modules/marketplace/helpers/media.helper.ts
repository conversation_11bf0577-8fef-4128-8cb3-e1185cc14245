import { Injectable } from '@nestjs/common';
import { ImageTypeEnum, MediaType } from '@shared/utils';

/**
 * Helper class cho việc xử lý và chuyển đổi dữ liệu media trong marketplace
 */
@Injectable()
export class MediaHelper {
  /**
   * Xác định MediaType từ MIME type string
   * @param mimeType MIME type string (ví dụ: 'image/jpeg', 'image/png')
   * @returns ImageTypeEnum tương ứng hoặc ImageTypeEnum.JPEG nếu không tìm thấy
   */
  getImageTypeFromMimeString(mimeType: string): MediaType {
    if (mimeType === 'image/jpeg' || mimeType === 'image/jpg') {
      return ImageTypeEnum.JPEG;
    } else if (mimeType === 'image/png') {
      return ImageTypeEnum.PNG;
    } else if (mimeType === 'image/webp') {
      return ImageTypeEnum.WEBP;
    } else if (mimeType === 'image/gif') {
      return ImageTypeEnum.GIF;
    } else {
      // Mặc định sử dụng JPEG
      return ImageTypeEnum.JPEG;
    }
  }
}
