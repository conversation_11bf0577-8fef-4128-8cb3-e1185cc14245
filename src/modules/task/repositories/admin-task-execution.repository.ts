import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminTaskExecution } from '../entities/admin-task-execution.entity';

/**
 * Repository cho entity AdminTaskExecution
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng admin_task_executions
 */
@Injectable()
export class AdminTaskExecutionRepository extends Repository<AdminTaskExecution> {
  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(AdminTaskExecution, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AdminTaskExecution
   * @returns SelectQueryBuilder cho AdminTaskExecution
   */
  private createBaseQuery(): SelectQueryBuilder<AdminTaskExecution> {
    return this.createQueryBuilder('execution');
  }
}
