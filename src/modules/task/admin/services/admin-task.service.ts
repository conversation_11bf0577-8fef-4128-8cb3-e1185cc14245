import { Injectable, Logger } from '@nestjs/common';
import { AdminTaskRepository, AdminTaskExecutionRepository } from '../../repositories';

/**
 * Service xử lý logic liên quan đến task của admin
 */
@Injectable()
export class AdminTaskService {
  /**
   * Logger cho AdminTaskService
   */
  private readonly logger = new Logger(AdminTaskService.name);

  /**
   * Constructor
   * @param adminTaskRepository Repository xử lý dữ liệu task của admin
   * @param adminTaskExecutionRepository Repository xử lý dữ liệu thực thi task của admin
   */
  constructor(
    private readonly adminTaskRepository: AdminTaskRepository,
    private readonly adminTaskExecutionRepository: AdminTaskExecutionRepository,
  ) {}
}
