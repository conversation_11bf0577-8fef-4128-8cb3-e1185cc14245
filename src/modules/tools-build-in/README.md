# Module Tools Build-In

Module này chứa các công cụ tích hợp sẵn (built-in tools) cho hệ thống.

## C<PERSON>u trúc thư mục

```
/tools-build-in
  /controllers    # Controllers xử lý các request API
  /services       # Services chứa logic nghiệp vụ
  /dto            # Data Transfer Objects
  /entities       # Entities định nghĩa schema database
  /repositories   # Repositories truy vấn database
  /interfaces     # Interfaces và types
  /constants      # Hằng số và enums
  /exceptions     # Exceptions và error codes
  /helpers        # Helper functions
```

## Mục đích

Module này cung cấp các công cụ tích hợp sẵn cho hệ thống, không phân chia theo admin/user mà sử dụng chung cho toàn bộ hệ thống.

## Cách sử dụng

Để sử dụng module này, cần import `ToolsBuildInModule` vào module cần sử dụng:

```typescript
import { Module } from '@nestjs/common';
import { ToolsBuildInModule } from '../tools-build-in/tools-build-in.module';

@Module({
  imports: [
    ToolsBuildInModule,
    // Các module khác
  ],
})
export class YourModule {}
```
