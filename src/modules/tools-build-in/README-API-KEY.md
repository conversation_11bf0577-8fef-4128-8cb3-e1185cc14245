# Hướng dẫn sử dụng API Key Authentication

Module này cung cấp một cơ chế xác thực API Key cho các endpoint trong module `tools-build-in`. Tài liệu này mô tả cách sử dụng API Key Authentication.

## Cấu hình API Key

API Key được cấu hình thông qua biến môi trường:

```
API_KEY=your_api_key_here
```

Bạn cũng có thể cấu hình nhiều API Key bằng cách sử dụng biến môi trường `ADDITIONAL_API_KEYS`:

```
ADDITIONAL_API_KEYS=key1,key2,key3
```

## Sử dụng API Key Authentication

### 1. Bảo vệ Controller hoặc Route

Để bảo vệ một controller hoặc route cụ thể, sử dụng `ApiKeyAuthGuard` và `@ApiKeyAuth()` decorator:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiKeyAuthGuard } from '@modules/tools-build-in/guards';
import { ApiKeyAuth } from '@modules/tools-build-in/decorators';

@Controller('api/v1/tools')
@UseGuards(ApiKeyAuthGuard)  // Áp dụng cho toàn bộ controller
@ApiKeyAuth()                // Đánh dấu toàn bộ controller yêu cầu API Key
export class ToolsController {
  @Get()
  getAllTools() {
    // Endpoint này yêu cầu API Key
    return { tools: [] };
  }

  @Get('public')
  @ApiKeyAuth(false)  // Đánh dấu endpoint này không yêu cầu API Key
  getPublicTools() {
    // Endpoint này không yêu cầu API Key
    return { publicTools: [] };
  }
}
```

Hoặc áp dụng cho một route cụ thể:

```typescript
@Get('secure')
@UseGuards(ApiKeyAuthGuard)  // Chỉ áp dụng cho route này
@ApiKeyAuth()                // Đánh dấu route này yêu cầu API Key
getSecureTools() {
  // Endpoint này yêu cầu API Key
  return { secureTools: [] };
}
```

### 2. Gửi API Key trong Request

Khi gọi API, bạn cần gửi API Key trong header `x-api-key`:

```
GET /api/v1/tools HTTP/1.1
Host: example.com
x-api-key: your_api_key_here
```

Ví dụ với cURL:

```bash
curl -X GET "https://example.com/api/v1/tools" -H "x-api-key: your_api_key_here"
```

### 3. Xử lý lỗi

Nếu API Key không được cung cấp hoặc không hợp lệ, API sẽ trả về lỗi:

- **API Key không được cung cấp**: Status 401, Code 30201
- **API Key không hợp lệ**: Status 401, Code 30202
- **API Key đã hết hạn**: Status 401, Code 30203
- **API Key không có quyền truy cập**: Status 403, Code 30204

## Ví dụ

### Bảo vệ Facebook API

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiKeyAuthGuard } from '@modules/tools-build-in/guards';
import { ApiKeyAuth } from '@modules/tools-build-in/decorators';
import { FacebookService } from '../services';

@Controller('api/v1/facebook')
@UseGuards(ApiKeyAuthGuard)
@ApiKeyAuth()
export class FacebookController {
  constructor(private readonly facebookService: FacebookService) {}

  @Get('pages')
  getPages() {
    return this.facebookService.getPages();
  }
}
```

### Bảo vệ một endpoint cụ thể

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiKeyAuthGuard } from '@modules/tools-build-in/guards';
import { ApiKeyAuth } from '@modules/tools-build-in/decorators';
import { WebsiteService } from '../services';

@Controller('api/v1/website')
export class WebsiteController {
  constructor(private readonly websiteService: WebsiteService) {}

  @Get('public-info')
  getPublicInfo() {
    // Endpoint này không yêu cầu API Key
    return this.websiteService.getPublicInfo();
  }

  @Get('analysis')
  @UseGuards(ApiKeyAuthGuard)
  @ApiKeyAuth()
  getAnalysis() {
    // Endpoint này yêu cầu API Key
    return this.websiteService.getAnalysis();
  }
}
```
