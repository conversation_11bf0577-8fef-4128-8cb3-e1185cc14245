import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { TOOLS_BUILD_IN_API_KEY_ERROR_CODES } from '../exceptions';

/**
 * Interface định nghĩa thông tin agent đư<PERSON><PERSON> lưu trong request
 */
export interface AgentInfo {
  id: string;
  userId: number;
}

/**
 * Decorator để lấy thông tin agent từ request
 * Thông tin này được lưu bởi ApiKeyAuthGuard sau khi giải mã API key
 * 
 * @param dataOrPipe Tên thuộc tính cần lấy từ agent hoặc pipe để transform dữ liệu
 * @returns Decorator
 * 
 * @example
 * // Lấy toàn bộ thông tin agent
 * @CurrentAgent() agent: AgentInfo
 * 
 * // Lấy ID của agent
 * @CurrentAgent('id') agentId: string
 * 
 * // Lấy ID của user sở hữu agent
 * @CurrentAgent('userId') userId: number
 */
export const CurrentAgent = createParamDecorator(
  (data: keyof AgentInfo | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const agent = request['agent'];

    // Kiểm tra xem agent có tồn tại không
    if (!agent) {
      throw new AppException(
        TOOLS_BUILD_IN_API_KEY_ERROR_CODES.AGENT_NOT_FOUND,
        'Không tìm thấy thông tin agent trong request',
      );
    }

    // Nếu không có data, trả về toàn bộ agent
    if (!data) {
      return agent;
    }

    // Nếu có data, trả về thuộc tính tương ứng
    return agent[data];
  },
);
