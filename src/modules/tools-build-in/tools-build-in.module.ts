import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { S3Service } from '@shared/services/s3.service';
import {
  WebsiteController,
  AgentToolsController,
} from './controllers';
import {
  AddressService,
  ConversionService,
  ProductService,
  SendService,
  WebsiteService,
  AgentToolsService
} from './services';
import { ApiKeyAuthGuard } from './guards';
import { ApiKeyUtil } from './utils';

@Module({
  imports: [
    TypeOrmModule.forFeature([
    ]),
    HttpModule,
    ConfigModule
  ],
  controllers: [
    WebsiteController,
    AgentToolsController,
  ],
  providers: [
    WebsiteService,
    AddressService,
    ProductService,
    ConversionService,
    SendService,
    S3Service,
    ApiKeyAuthGuard,
    AgentToolsService,
    ApiKeyUtil
  ],
  exports: [
    WebsiteService,
    AddressService,
    ProductService,
    ConversionService,
    SendService,
    S3Service,
    ApiKeyAuthGuard,
    AgentToolsService,
    ApiKeyUtil
  ],
})
export class ToolsBuildInModule { }
