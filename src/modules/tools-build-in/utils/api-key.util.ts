import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

/**
 * Utility để mã hóa và giải mã API Key
 */
@Injectable()
export class ApiKeyUtil {
  private readonly logger = new Logger(ApiKeyUtil.name);
  private readonly apiPrefixKey: string;
  private readonly apiSecretKey: string;
  private readonly algorithm = 'aes-256-cbc';

  constructor(private readonly configService: ConfigService) {
    this.apiPrefixKey = this.configService.get<string>('API_PREFIX_KEY') || 'redai';
    this.apiSecretKey = this.configService.get<string>('API_SERECT_KEY') || '';
    
    if (!this.apiSecretKey) {
      this.logger.warn('API_SERECT_KEY không được cấu hình. Xác thực API Key sẽ không hoạt động đúng.');
    }
  }

  /**
   * Tạo API Key từ Agent ID và User ID
   * @param agentId ID của agent
   * @param userId ID của user
   * @returns API Key
   */
  generateApiKey(agentId: string, userId: number): string {
    try {
      // Tạo chuỗi dữ liệu cần mã hóa
      const data = JSON.stringify({ agentId, userId });
      
      // Tạo key và iv từ secret key
      const key = this.generateKey();
      const iv = this.generateIv();
      
      // Mã hóa dữ liệu
      const cipher = crypto.createCipheriv(this.algorithm, key, iv);
      let encrypted = cipher.update(data, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      // Kết hợp iv và encrypted data để có thể giải mã sau này
      const result = iv.toString('base64') + ':' + encrypted;
      
      // Tạo API Key hoàn chỉnh
      return `${this.apiPrefixKey}_${result}`;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo API Key: ${error.message}`, error.stack);
      throw new Error('Không thể tạo API Key');
    }
  }

  /**
   * Giải mã API Key để lấy Agent ID và User ID
   * @param apiKey API Key cần giải mã
   * @returns Object chứa Agent ID và User ID, hoặc null nếu không giải mã được
   */
  decodeApiKey(apiKey: string): { agentId: string; userId: number } | null {
    try {
      // Kiểm tra xem API Key có đúng định dạng không
      if (!apiKey || !apiKey.startsWith(`${this.apiPrefixKey}_`)) {
        return null;
      }
      
      // Tách phần prefix và phần mã hóa
      const encryptedPart = apiKey.substring(this.apiPrefixKey.length + 1);
      
      // Tách iv và encrypted data
      const parts = encryptedPart.split(':');
      if (parts.length !== 2) {
        return null;
      }
      
      const iv = Buffer.from(parts[0], 'base64');
      const encrypted = parts[1];
      
      // Tạo key từ secret key
      const key = this.generateKey();
      
      // Giải mã dữ liệu
      const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
      let decrypted = decipher.update(encrypted, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      // Parse dữ liệu giải mã
      const data = JSON.parse(decrypted);
      
      return {
        agentId: data.agentId,
        userId: data.userId,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã API Key: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tạo key từ secret key
   * @returns Buffer chứa key
   */
  private generateKey(): Buffer {
    // Sử dụng SHA-256 để tạo key 32 bytes từ secret key
    return crypto.createHash('sha256').update(this.apiSecretKey).digest();
  }

  /**
   * Tạo iv (initialization vector) ngẫu nhiên
   * @returns Buffer chứa iv
   */
  private generateIv(): Buffer {
    // Tạo iv 16 bytes ngẫu nhiên
    return crypto.randomBytes(16);
  }
}
