import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query parameters khi lấy danh sách template email
 */
export class TemplateEmailQueryDto extends QueryDto {

  /**
   * Tìm kiếm theo category
   * @example "ACCOUNT_VERIFICATION"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo category',
    example: 'ACCOUNT_VERIFICATION',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Category phải là chuỗi' })
  category?: string;

  /**
   * Tìm kiếm theo subject
   * @example "Xác thực tài khoản"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo subject',
    example: 'Xác thực tài khoản',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Subject phải là chuỗi' })
  subject?: string;


}
