import { Injectable } from '@nestjs/common';
import { In } from 'typeorm';
import { UserTemplateEmailRepository } from '../repositories/user-template-email.repository';
import { UserCampaignHistoryRepository } from '../repositories/user-campaign-history.repository';
import { UserCampaignRepository } from '../repositories/user-campaign.repository';
import { MarketingOverviewResponseDto, RecentTemplatesResponseDto, RecentTemplateDto, TemplateStatus } from '../dto/overview';
import { SendStatus } from '../dto/campaign/campaign-history-response.dto';

/**
 * Service xử lý logic overview marketing
 */
@Injectable()
export class MarketingOverviewService {
  constructor(
    private readonly userTemplateEmailRepository: UserTemplateEmailRepository,
    private readonly userCampaignHistoryRepository: UserCampaignHistoryRepository,
    private readonly userCampaignRepository: UserCampaignRepository,
  ) {}

  /**
   * Lấy thông tin overview marketing
   * @param userId ID của người dùng
   * @returns Thông tin overview
   */
  async getMarketingOverview(userId: number): Promise<MarketingOverviewResponseDto> {
    // Đếm tổng số templates
    const totalTemplates = await this.userTemplateEmailRepository.repository.count({
      where: { userId },
    });

    // Lấy tất cả campaigns của user (chỉ select các field cần thiết để tránh lỗi)
    const userCampaigns = await this.userCampaignRepository.repository.find({
      where: { userId },
      select: ['id', 'userId'], // Chỉ select các field cần thiết
    });

    const campaignIds = userCampaigns.map(c => c.id);

    // Lấy campaign history cho các campaigns của user
    let allHistory: any[] = [];
    if (campaignIds.length > 0) {
      allHistory = await this.userCampaignHistoryRepository.repository.find({
        where: { campaignId: In(campaignIds) },
      });
    }

    // Tính toán metrics từ campaign history
    const sentEmails = allHistory.filter(h =>
      h.status === SendStatus.SENT ||
      h.status === SendStatus.DELIVERED ||
      h.status === SendStatus.OPENED ||
      h.status === SendStatus.CLICKED
    );

    const openedEmails = allHistory.filter(h =>
      h.status === SendStatus.OPENED ||
      h.status === SendStatus.CLICKED
    );

    const clickedEmails = allHistory.filter(h =>
      h.status === SendStatus.CLICKED
    );

    // Tính tỷ lệ
    const totalEmailsSent = sentEmails.length;
    const openRate = totalEmailsSent > 0 ? (openedEmails.length / totalEmailsSent) * 100 : 0;
    const clickRate = totalEmailsSent > 0 ? (clickedEmails.length / totalEmailsSent) * 100 : 0;

    return {
      totalTemplates,
      openRate: Math.round(openRate * 10) / 10, // Làm tròn 1 chữ số thập phân
      clickRate: Math.round(clickRate * 10) / 10,
      totalEmailsSent,
    };
  }

  /**
   * Lấy danh sách 5 templates gần đây nhất
   * @param userId ID của người dùng
   * @returns Danh sách templates gần đây
   */
  async getRecentTemplates(userId: number): Promise<RecentTemplatesResponseDto> {
    // Lấy 5 templates gần nhất (chỉ select các field cần thiết để tránh lỗi)
    const templates = await this.userTemplateEmailRepository.repository.find({
      where: { userId },
      select: ['id', 'name', 'subject', 'createdAt'], // Bỏ status tạm thời
      order: { createdAt: 'DESC' },
      take: 5,
    });

    // Chuyển đổi sang DTO
    const recentTemplates: RecentTemplateDto[] = templates.map(template => ({
      id: template.id,
      name: template.name,
      subject: template.subject,
      status: TemplateStatus.DRAFT, // Tạm thời set mặc định là DRAFT
      createdAt: template.createdAt,
    }));

    return {
      templates: recentTemplates,
    };
  }

  // TODO: Sau khi chạy migration, sẽ thêm lại logic xử lý status
  // /**
  //  * Map status từ string sang enum
  //  * @param status Status string
  //  * @returns TemplateStatus enum
  //  */
  // private mapTemplateStatus(status: string): TemplateStatus {
  //   switch (status?.toUpperCase()) {
  //     case 'ACTIVE':
  //       return TemplateStatus.ACTIVE;
  //     case 'INACTIVE':
  //       return TemplateStatus.INACTIVE;
  //     case 'DRAFT':
  //     default:
  //       return TemplateStatus.DRAFT;
  //   }
  // }
}
