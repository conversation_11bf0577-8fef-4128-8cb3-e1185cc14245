import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ZaloFollower } from '../entities/zalo-follower.entity';

/**
 * Repository cho ZaloFollower
 */
@Injectable()
export class ZaloFollowerRepository {
  constructor(
    @InjectRepository(ZaloFollower)
    private readonly repository: Repository<ZaloFollower>,
  ) {}

  /**
   * Tìm kiếm nhiều người theo dõi
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách người theo dõi
   */
  async find(options?: FindManyOptions<ZaloFollower>): Promise<ZaloFollower[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một người theo dõi
   * @param options Tùy chọn tìm kiếm
   * @returns Người theo dõi hoặc null
   */
  async findOne(options?: FindOneOptions<ZaloFollower>): Promise<ZaloFollower | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm người theo dõi theo ID
   * @param id ID của người theo dõi
   * @returns Người theo dõi hoặc null
   */
  async findById(id: number): Promise<ZaloFollower | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm người theo dõi theo ID Official Account và ID người dùng Zalo
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @returns Người theo dõi hoặc null
   */
  async findByOaIdAndUserId(oaId: string, userId: string): Promise<ZaloFollower | null> {
    return this.repository.findOne({ where: { oaId, userId } });
  }

  /**
   * Tìm tất cả người theo dõi của một Official Account
   * @param oaId ID của Official Account
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách người theo dõi
   */
  async findByOaId(oaId: string, options?: FindManyOptions<ZaloFollower>): Promise<ZaloFollower[]> {
    const findOptions: FindManyOptions<ZaloFollower> = {
      where: { oaId },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tìm tất cả người theo dõi theo trạng thái
   * @param oaId ID của Official Account
   * @param status Trạng thái người theo dõi
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách người theo dõi
   */
  async findByOaIdAndStatus(
    oaId: string,
    status: string,
    options?: FindManyOptions<ZaloFollower>,
  ): Promise<ZaloFollower[]> {
    const findOptions: FindManyOptions<ZaloFollower> = {
      where: { oaId, status },
      ...options,
    };
    return this.repository.find(findOptions);
  }

  /**
   * Tìm tất cả người theo dõi có chứa tag
   * @param oaId ID của Official Account
   * @param tag Tag cần tìm
   * @param options Tùy chọn tìm kiếm bổ sung
   * @returns Danh sách người theo dõi
   */
  async findByOaIdAndTag(oaId: string, tag: string, options?: FindManyOptions<ZaloFollower>): Promise<ZaloFollower[]> {
    // Sử dụng raw query để tìm kiếm trong mảng JSONB
    const query = this.repository
      .createQueryBuilder('follower')
      .where('follower.oa_id = :oaId', { oaId })
      .andWhere(`follower.tags @> :tag`, { tag: JSON.stringify([tag]) });

    // Áp dụng các tùy chọn bổ sung nếu có
    if (options?.order) {
      const orderKey = Object.keys(options.order)[0];
      const orderValue = options.order[orderKey];
      query.orderBy(`follower.${orderKey}`, orderValue as 'ASC' | 'DESC');
    }

    if (options?.skip) {
      query.skip(options.skip);
    }

    if (options?.take) {
      query.take(options.take);
    }

    return query.getMany();
  }

  /**
   * Tạo mới người theo dõi
   * @param data Dữ liệu người theo dõi
   * @returns Người theo dõi đã tạo
   */
  async create(data: Partial<ZaloFollower>): Promise<ZaloFollower> {
    const follower = this.repository.create(data);
    return this.repository.save(follower);
  }

  /**
   * Tạo nhiều người theo dõi
   * @param dataArray Mảng dữ liệu người theo dõi
   * @returns Danh sách người theo dõi đã tạo
   */
  async createMany(dataArray: Partial<ZaloFollower>[]): Promise<ZaloFollower[]> {
    const followers = this.repository.create(dataArray);
    return this.repository.save(followers);
  }

  /**
   * Cập nhật người theo dõi
   * @param id ID của người theo dõi
   * @param data Dữ liệu cập nhật
   * @returns Người theo dõi đã cập nhật
   */
  async update(id: number, data: Partial<ZaloFollower>): Promise<ZaloFollower | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Cập nhật người theo dõi theo ID Official Account và ID người dùng Zalo
   * @param oaId ID của Official Account
   * @param userId ID của người dùng Zalo
   * @param data Dữ liệu cập nhật
   * @returns Người theo dõi đã cập nhật
   */
  async updateByOaIdAndUserId(
    oaId: string,
    userId: string,
    data: Partial<ZaloFollower>,
  ): Promise<ZaloFollower | null> {
    await this.repository.update({ oaId, userId }, data);
    return this.findByOaIdAndUserId(oaId, userId);
  }

  /**
   * Thêm tag cho người theo dõi
   * @param id ID của người theo dõi
   * @param tag Tag cần thêm
   * @returns Người theo dõi đã cập nhật
   */
  async addTag(id: number, tag: string): Promise<ZaloFollower | null> {
    const follower = await this.findById(id);
    if (!follower) {
      return null;
    }

    // Thêm tag nếu chưa tồn tại
    if (!follower.tags.includes(tag)) {
      follower.tags.push(tag);
      follower.updatedAt = Date.now();
      return this.repository.save(follower);
    }

    return follower;
  }

  /**
   * Xóa tag của người theo dõi
   * @param id ID của người theo dõi
   * @param tag Tag cần xóa
   * @returns Người theo dõi đã cập nhật
   */
  async removeTag(id: number, tag: string): Promise<ZaloFollower | null> {
    const follower = await this.findById(id);
    if (!follower) {
      return null;
    }

    // Xóa tag nếu tồn tại
    const tagIndex = follower.tags.indexOf(tag);
    if (tagIndex !== -1) {
      follower.tags.splice(tagIndex, 1);
      follower.updatedAt = Date.now();
      return this.repository.save(follower);
    }

    return follower;
  }

  /**
   * Xóa người theo dõi
   * @param id ID của người theo dõi
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Đếm số lượng người theo dõi
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng người theo dõi
   */
  async count(options?: FindManyOptions<ZaloFollower>): Promise<number> {
    return this.repository.count(options);
  }
}
