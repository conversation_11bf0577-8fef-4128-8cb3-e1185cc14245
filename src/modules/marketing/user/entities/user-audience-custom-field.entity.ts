import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_audience_custom_fields trong cơ sở dữ liệu
 * Bảng danh sách trường tùy chỉnh của audience
 */
@Entity('user_audience_custom_fields')
export class UserAudienceCustomField {
  /**
   * ID của custom field
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của audience
   */
  @Column({ name: 'audience_id', nullable: true })
  audienceId: number;

  // Không sử dụng quan hệ với bảng UserAudience, chỉ lưu ID

  /**
   * Tên trường
   */
  @Column({ name: 'field_name', length: 255, nullable: true, comment: 'Tên trường' })
  fieldName: string;

  /**
   * Giá trị trường
   */
  @Column({ name: 'field_value', type: 'jsonb', nullable: true, comment: '<PERSON><PERSON><PERSON> trị' })
  fieldValue: any;

  /**
   * <PERSON><PERSON><PERSON> dữ liệu của trường
   */
  @Column({ name: 'field_type', length: 20, nullable: true, comment: 'Kiểu giá trị' })
  fieldType: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
