import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng google_ads_accounts trong cơ sở dữ liệu
 * Lưu trữ thông tin tài khoản Google Ads của người dùng
 */
@Entity('google_ads_accounts')
export class GoogleAdsAccount {
  /**
   * ID của tài khoản Google Ads trong hệ thống
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  /**
   * Customer ID của tài khoản Google Ads
   */
  @Column({ name: 'customer_id', nullable: false, comment: 'Customer ID của tài khoản Google Ads' })
  customerId: string;

  /**
   * Refresh token để truy cập Google Ads API
   */
  @Column({ name: 'refresh_token', nullable: false, comment: 'Refresh token để truy cập Google Ads API' })
  refreshToken: string;

  /**
   * Tên tài khoản
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên tài khoản' })
  name: string;

  /**
   * Trạng thái tài khoản
   */
  @Column({ name: 'status', length: 20, nullable: false, default: 'active', comment: 'Trạng thái tài khoản' })
  status: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
