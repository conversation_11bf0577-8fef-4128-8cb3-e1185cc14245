import { Controller, Post, Body, UseGuards, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { EmailMarketingService } from '../services/email-marketing.service';
import { CreateEmailCampaignDto, CreateEmailCampaignResponseDto, RecentCampaignsResponseDto, EmailCampaignOverviewResponseDto, RecentCampaignDto } from '../dto/email-campaign';
import { EmailCampaignQueryDto } from '../dto/email-campaign/email-campaign-query.dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';

/**
 * Controller xử lý API liên quan đến email campaign
 */
@ApiTags(SWAGGER_API_TAGS.USER_EMAIL_CAMPAIGN)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/email-campaigns')
export class EmailCampaignController {
  constructor(private readonly emailMarketingService: EmailMarketingService) {}

  /**
   * Tạo email campaign và đẩy jobs vào queue
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo email campaign',
    description: 'Tạo chiến dịch email marketing mới, lưu vào database và tạo jobs đẩy vào queue để worker xử lý'
  })
  @ApiResponse({
    status: 201,
    description: 'Email campaign đã được tạo thành công và jobs đã được đẩy vào queue',
    type: CreateEmailCampaignResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ hoặc không tìm thấy audience'
  })
  @ApiResponse({
    status: 404,
    description: 'Segment hoặc audience không tồn tại'
  })
  async createEmailCampaign(
    @CurrentUser() user: JwtPayload,
    @Body() createEmailCampaignDto: CreateEmailCampaignDto,
  ): Promise<AppApiResponse<CreateEmailCampaignResponseDto>> {
    const result = await this.emailMarketingService.createEmailCampaign(user.id, createEmailCampaignDto);
    return wrapResponse(
      result,
      `Email campaign đã được tạo thành công với ${result.jobCount} jobs. Campaign sẽ được xử lý bởi worker.`
    );
  }

  /**
   * Kiểm tra trạng thái email marketing queue
   */
  @Get('queue/status')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái queue',
    description: 'Lấy thông tin trạng thái hiện tại của email marketing queue'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trạng thái queue',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Trạng thái queue' },
        data: {
          type: 'object',
          properties: {
            waiting: { type: 'number', example: 10, description: 'Số job đang chờ xử lý' },
            active: { type: 'number', example: 2, description: 'Số job đang được xử lý' },
            completed: { type: 'number', example: 100, description: 'Số job đã hoàn thành' },
            failed: { type: 'number', example: 5, description: 'Số job thất bại' },
          },
        },
      },
    },
  })
  async getQueueStatus(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<any>> {
    const result = await this.emailMarketingService.getQueueStatus();
    return wrapResponse(result, 'Trạng thái email marketing queue');
  }

  /**
   * Lấy danh sách chiến dịch email có phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách chiến dịch email có phân trang',
    description: 'Lấy danh sách các chiến dịch email với phân trang, filter theo trạng thái và tìm kiếm theo tiêu đề'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách chiến dịch email có phân trang',
    schema: AppApiResponse.getPaginatedSchema(RecentCampaignDto)
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getEmailCampaigns(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: EmailCampaignQueryDto,
  ): Promise<AppApiResponse<PaginatedResult<RecentCampaignDto>>> {
    const result = await this.emailMarketingService.getRecentCampaignsPaginated(user.id, queryDto);
    return AppApiResponse.paginated(result, 'Lấy danh sách chiến dịch email thành công');
  }

  /**
   * Lấy danh sách chiến dịch email gần đây
   */
  @Get('recent')
  @ApiOperation({
    summary: 'Lấy chiến dịch email gần đây',
    description: 'Lấy danh sách các chiến dịch email gần đây với thông tin tổng số người nhận, trạng thái, thời gian chạy và tỷ lệ gửi/click'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách chiến dịch email gần đây',
    type: RecentCampaignsResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getRecentCampaigns(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<RecentCampaignsResponseDto>> {
    const result = await this.emailMarketingService.getRecentCampaigns(user.id);
    return wrapResponse(result, 'Danh sách chiến dịch email gần đây');
  }

  /**
   * Lấy thống kê tổng quan email campaign
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy thống kê tổng quan email campaign',
    description: 'Lấy thống kê tổng số chiến dịch, đang gửi, đã gửi, đã lên lịch'
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê tổng quan email campaign',
    type: EmailCampaignOverviewResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập'
  })
  async getOverview(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<EmailCampaignOverviewResponseDto>> {
    const result = await this.emailMarketingService.getOverview(user.id);
    return wrapResponse(result, 'Thống kê tổng quan email campaign');
  }
}
