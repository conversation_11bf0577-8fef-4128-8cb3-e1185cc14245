import { ApiProperty } from '@nestjs/swagger';
import { TransactionStatus } from '@modules/r-point/enums';

/**
 * DTO cho thông tin point trong response
 */
export class UserTransactionPointDto {
  @ApiProperty({
    description: 'ID của gói point',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên của gói point',
    example: 'Gói 100k'
  })
  name: string;

  @ApiProperty({
    description: 'Số tiền của gói point',
    example: 100000
  })
  cash: number;

  @ApiProperty({
    description: 'Tỷ lệ quy đổi',
    example: 1000
  })
  rate: number;
}

/**
 * DTO cho response trả về thông tin giao dịch
 */
export class UserTransactionResponseDto {
  @ApiProperty({
    description: 'ID của giao dịch',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Số tiền giao dịch',
    example: 100000
  })
  amount: number;

  @ApiProperty({
    description: 'Số lượng point mua',
    example: 100
  })
  pointsAmount: number;

  @ApiProperty({
    description: 'Tên loại point (nếu có)',
    example: 'Gói 100k'
  })
  pointName: string;

  @ApiProperty({
    description: 'Trạng thái giao dịch',
    enum: TransactionStatus,
    example: TransactionStatus.CONFIRMED
  })
  status: TransactionStatus;

  @ApiProperty({
    description: 'Số tiền giảm giá từ coupon',
    example: 10000
  })
  couponAmount: number;

  @ApiProperty({
    description: 'Thời gian tạo giao dịch',
    example: 1625097600000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian hoàn thành giao dịch',
    example: 1625097660000
  })
  completedAt: number;
}

/**
 * DTO cho response trả về danh sách giao dịch có phân trang
 */
export class UserTransactionPaginatedResponseDto {
  @ApiProperty({
    description: 'Danh sách giao dịch',
    type: [UserTransactionResponseDto]
  })
  items: UserTransactionResponseDto[];

  @ApiProperty({
    description: 'Tổng số giao dịch',
    example: 100
  })
  total: number;

  @ApiProperty({
    description: 'Số trang hiện tại',
    example: 1
  })
  page: number;

  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10
  })
  limit: number;

  @ApiProperty({
    description: 'Tổng số trang',
    example: 10
  })
  totalPages: number;
}
