import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CouponUserService } from '@modules/r-point/user/services';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { RandomCouponRequestDto, ValidateCouponRequestDto } from '../dto/coupon-request.dto';
import { CouponResponseDto, ValidateCouponResponseDto } from '../dto/coupon-response.dto';
import { ApiResponseDto } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';

@ApiTags('R-Point - User Coupons')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('user/r-point/coupons')
export class CouponUserController {
  constructor(
    private readonly couponUserService: CouponUserService
  ) {}

  /**
   * Lấy ngẫu nhiên 2-3 coupon đủ điều kiện sử dụng cho đơn hàng
   * @param dto Thông tin về gói point và số lượng point
   * @param user Thông tin người dùng hiện tại
   * @returns Danh sách coupon phù hợp
   */
  @Post('random')
  @ApiOperation({ summary: 'Lấy ngẫu nhiên 2-3 coupon phù hợp với đơn hàng' })
  @ApiResponse({ status: 200, description: 'Danh sách coupon phù hợp', type: [CouponResponseDto] })
  async getRandomCoupons(
    @Body() dto: RandomCouponRequestDto,
  ): Promise<ApiResponseDto<CouponResponseDto[]>> {
    const coupons = await this.couponUserService.getRandomCoupons(dto);
    return ApiResponseDto.success(coupons, 'Lấy danh sách coupon phù hợp thành công');
  }

  /**
   * Kiểm tra mã coupon có hợp lệ và áp dụng được cho đơn hàng không
   * @param dto Thông tin về mã coupon, gói point và số lượng point
   * @param user Thông tin người dùng hiện tại
   * @returns Kết quả kiểm tra coupon
   */
  @Post('validate')
  @ApiOperation({ summary: 'Kiểm tra mã coupon có hợp lệ và áp dụng được cho đơn hàng không' })
  @ApiResponse({ status: 200, description: 'Kết quả kiểm tra coupon', type: ValidateCouponResponseDto })
  async validateCoupon(
    @Body() dto: ValidateCouponRequestDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<ValidateCouponResponseDto>> {
    const result = await this.couponUserService.validateCoupon(dto, user.id);
    return ApiResponseDto.success(result, 'Kiểm tra mã giảm giá thành công');
  }


}
