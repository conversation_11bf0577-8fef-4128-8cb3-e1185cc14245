import { Controller, Get, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { TransactionAdminService } from '../services';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { StatisticsResponseDto, TransactionPaginatedResponseDto, TransactionQueryDto, TransactionResponseDto } from '../dto';

@ApiTags('R-Point - Admin Transactions')
@ApiBearerAuth()
@UseGuards(JwtEmployeeGuard)
@Controller('admin/r-point/transactions')
export class TransactionUserController {
  constructor(
    private readonly transactionAdminService: TransactionAdminService,
  ) {}

  /**
   * <PERSON><PERSON>y danh sách lịch sử giao dịch của khách hàng với phân trang và lọc
   * @param queryDto Tham số truy vấn
   * @returns Danh sách giao dịch và thông tin phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách lịch sử giao dịch của khách hàng' })
  @ApiResponse({ status: 200, description: 'Danh sách giao dịch', type: TransactionPaginatedResponseDto })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (bắt đầu từ 1)', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng item trên mỗi trang', type: Number })
  @ApiQuery({ name: 'userId', required: false, description: 'ID của người dùng', type: Number })
  @ApiQuery({ name: 'status', required: false, description: 'Trạng thái giao dịch' })
  @ApiQuery({ name: 'keyword', required: false, description: 'Từ khóa tìm kiếm' })
  @ApiQuery({ name: 'startTime', required: false, description: 'Thời gian bắt đầu (Unix timestamp)', type: Number })
  @ApiQuery({ name: 'endTime', required: false, description: 'Thời gian kết thúc (Unix timestamp)', type: Number })
  async getUserTransactions(@Query() queryDto: TransactionQueryDto): Promise<AppApiResponse<TransactionPaginatedResponseDto>> {
    const result = await this.transactionAdminService.getUserTransactions(queryDto);
    return AppApiResponse.success(result, 'Lấy danh sách giao dịch thành công');
  }

  /**
   * Lấy thông tin chi tiết của một giao dịch
   * @param id ID của giao dịch
   * @returns Thông tin chi tiết giao dịch
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết của một giao dịch' })
  @ApiResponse({ status: 200, description: 'Thông tin chi tiết giao dịch', type: TransactionResponseDto })
  @ApiParam({ name: 'id', description: 'ID của giao dịch', type: Number })
  async getTransactionById(@Param('id', ParseIntPipe) id: number): Promise<AppApiResponse<TransactionResponseDto>> {
    const transaction = await this.transactionAdminService.getTransactionById(id);
    return AppApiResponse.success(transaction, 'Lấy thông tin giao dịch thành công');
  }

  /**
   * Lấy thống kê về r-point và giao dịch
   * @param startTime Thời gian bắt đầu (tùy chọn)
   * @param endTime Thời gian kết thúc (tùy chọn)
   * @returns Thống kê chi tiết
   */
  @Get('statistics/overview')
  @ApiOperation({ summary: 'Lấy thống kê về r-point và giao dịch' })
  @ApiResponse({ status: 200, description: 'Thống kê chi tiết', type: StatisticsResponseDto })
  @ApiQuery({ name: 'startTime', required: false, description: 'Thời gian bắt đầu (Unix timestamp)', type: Number })
  @ApiQuery({ name: 'endTime', required: false, description: 'Thời gian kết thúc (Unix timestamp)', type: Number })
  async getStatistics(
    @Query('startTime') startTime?: number,
    @Query('endTime') endTime?: number,
  ): Promise<AppApiResponse<StatisticsResponseDto>> {
    const statistics = await this.transactionAdminService.getStatistics(startTime, endTime);
    return AppApiResponse.success(statistics, 'Lấy thống kê thành công');
  }
}
