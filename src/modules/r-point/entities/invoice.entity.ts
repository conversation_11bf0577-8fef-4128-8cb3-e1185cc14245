import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { InvoiceStatus } from '@modules/r-point/enums';

/**
 * Entity đại diện cho bảng invoice trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin về hóa đơn mua point
 */
@Entity('invoice')
export class Invoice {
  /**
   * ID tự tăng của hóa đơn
   */
  @PrimaryGeneratedColumn('identity', { name: 'id' })
  id: number;

  /**
   * ID của giao dịch mua point
   */
  @Column({ name: 'order_id', type: 'bigint', comment: 'mã đơn hàng' })
  orderId: number;

  // Không sử dụng quan hệ với bảng point_purchase_transactions, chỉ lưu ID

  /**
   * Đường link PDF của hóa đơn
   */
  @Column({ name: 'invoice_path_pdf', length: 255, nullable: true, comment: 'Đường link PDF của hóa đơn' })
  invoicePathPdf: string;

  /**
   * Mẫu số hóa đơn
   */
  @Column({ name: 'inv_pattern', length: 20, nullable: true, comment: 'Mẫu số hóa đơn' })
  invPattern: string;

  /**
   * Mẫu số ký hiệu
   */
  @Column({ name: 'inv_serial', length: 20, nullable: true, comment: 'Mẫu số ký hiệu' })
  invSerial: string;

  /**
   * Thời điểm tạo hóa đơn
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Thời điểm tạo hóa đơn (Unix timestamp)' })
  createdAt: number;

  /**
   * Thời điểm cập nhật gần nhất
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời điểm cập nhật gần nhất (Unix timestamp)' })
  updatedAt: number;

  /**
   * Họ tên người mua hàng
   */
  @Column({ name: 'buyer_full_name', length: 255, nullable: true, comment: 'Họ tên người mua hàng' })
  buyerFullName: string;

  /**
   * Tên đơn vị (doanh nghiệp)
   */
  @Column({ name: 'company_name', length: 255, nullable: true, comment: 'Tên đơn vị (doanh nghiệp)' })
  companyName: string;

  /**
   * Mã số thuế của đơn vị mua hàng
   */
  @Column({ name: 'tax_code', length: 20, nullable: true, comment: 'Mã số thuế của đơn vị mua hàng' })
  taxCode: string;

  /**
   * Địa chỉ người mua
   */
  @Column({ name: 'address', length: 500, nullable: true, comment: 'Địa chỉ người mua' })
  address: string;

  /**
   * Hình thức thanh toán
   */
  @Column({ name: 'payment_method', length: 50, nullable: true, comment: 'Hình thức thanh toán (tiền mặt, chuyển khoản,...)' })
  paymentMethod: string;

  /**
   * Đơn vị tiền tệ
   */
  @Column({ name: 'currency', length: 10, nullable: true, comment: 'Đơn vị tiền tệ sử dụng trong hóa đơn (VD: VND, USD)' })
  currency: string;

  /**
   * Tài khoản ngân hàng của người mua
   */
  @Column({ name: 'account_number', length: 50, nullable: true, comment: 'Tài khoản ngân hàng của người mua (nếu có)' })
  accountNumber: string;

  /**
   * Mã bank code
   */
  @Column({ name: 'bank_code', length: 20, nullable: true, comment: 'mã bank code' })
  bankCode: string;

  /**
   * Tỷ giá nếu dùng đơn vị tiền tệ khác VND
   */
  @Column({ name: 'exchange_rate', type: 'double precision', nullable: true, comment: 'Tỷ giá nếu dùng đơn vị tiền tệ khác VND' })
  exchangeRate: number;

  /**
   * Trạng thái hóa đơn
   */
  @Column({ name: 'status', length: 20, nullable: true, comment: 'Trạng thái hóa đơn: PENDING (chưa xuất), ISSUED (đã xuất)' })
  status: InvoiceStatus;

  /**
   * Số tiền thuế GTGT phải nộp
   */
  @Column({ name: 'vat_amount', type: 'double precision', nullable: true, comment: 'Số tiền thuế GTGT phải nộp' })
  vatAmount: number;

  /**
   * Tên hàng hóa hoặc dịch vụ
   */
  @Column({ name: 'item_name', length: 255, nullable: true, comment: 'Tên hàng hóa hoặc dịch vụ' })
  itemName: string;

  /**
   * Đơn vị tính
   */
  @Column({ name: 'unit_of_measure', nullable: true, comment: 'Đơn vị tính (VD: cái, kg, giờ...)' })
  unitOfMeasure: number;

  /**
   * Số lượng hàng hóa/dịch vụ
   */
  @Column({ name: 'quantity', nullable: true, comment: 'Số lượng hàng hóa/dịch vụ' })
  quantity: number;

  /**
   * Đơn giá của từng đơn vị sản phẩm
   */
  @Column({ name: 'unit_price', type: 'double precision', nullable: true, comment: 'Đơn giá của từng đơn vị sản phẩm' })
  unitPrice: number;

  /**
   * Thành tiền (subtotal: quantity * unit_price)
   */
  @Column({ name: 'amount', type: 'double precision', nullable: true, comment: 'Thành tiền (subtotal: quantity * unit_price)' })
  amount: number;

  /**
   * Thuế suất giá trị gia tăng (GTGT) theo %
   */
  @Column({ name: 'vat_rate', type: 'double precision', nullable: true, comment: 'Thuế suất giá trị gia tăng (GTGT) theo %' })
  vatRate: number;

  /**
   * Tổng tiền thanh toán (đã bao gồm VAT)
   */
  @Column({ name: 'total_amount', type: 'double precision', nullable: true, comment: 'Tổng tiền thanh toán (đã bao gồm VAT)' })
  totalAmount: number;
}
