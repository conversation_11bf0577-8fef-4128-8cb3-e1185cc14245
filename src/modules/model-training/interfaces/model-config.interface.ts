import { AIProvider } from './fine-tuning-job.interface';

/**
 * Interface cho cấu hình model
 */
export interface ModelConfig {
  /** ID của model */
  id: string;
  /** Tên hiển thị của model */
  displayName: string;
  /** <PERSON><PERSON> tả về model */
  description?: string;
  /** Nhà cung cấp AI */
  provider: AIProvider;
  /** Giới hạn token đầu vào */
  inputTokenLimit?: number;
  /** Giới hạn token đầu ra */
  outputTokenLimit?: number;
  /** Các phương thức tạo được hỗ trợ */
  supportedGenerationMethods?: string[];
  /** Nhiệt độ mặc định */
  defaultTemperature?: number;
  /** Nhiệt độ tối đa */
  maxTemperature?: number;
  /** Top P mặc định */
  defaultTopP?: number;
  /** Top K mặc định */
  defaultTopK?: number;
  /** Có hỗ trợ function calling không */
  supportsFunctionCalling?: boolean;
  /** Có hỗ trợ xử lý hình ảnh không */
  supportsImageInput?: boolean;
  /** Có hỗ trợ xử lý âm thanh không */
  supportsAudioInput?: boolean;
  /** Có hỗ trợ xử lý video không */
  supportsVideoInput?: boolean;
  /** Có hỗ trợ parallel tool calling không */
  supportsParallelToolCalling?: boolean;
  /** Các mức độ reasoning effort được hỗ trợ */
  supportedReasoningEfforts?: string[];
  /** Có phải là model fine-tuned không */
  isFineTuned?: boolean;
  /** ID của model cơ sở (nếu là model fine-tuned) */
  baseModelId?: string;
  /** ID của fine-tuning job (nếu là model fine-tuned) */
  fineTuningJobId?: number;
  /** ID của người dùng sở hữu model (nếu là model fine-tuned) */
  userId?: number;
  /** Thời gian tạo */
  createdAt?: Date;
  /** Thời gian cập nhật cuối cùng */
  updatedAt?: Date;
}

/**
 * Interface cho tham số tạo cấu hình model
 */
export interface CreateModelConfigParams {
  /** ID của model */
  id: string;
  /** Tên hiển thị của model */
  displayName: string;
  /** Mô tả về model */
  description?: string;
  /** Nhà cung cấp AI */
  provider: AIProvider;
  /** Giới hạn token đầu vào */
  inputTokenLimit?: number;
  /** Giới hạn token đầu ra */
  outputTokenLimit?: number;
  /** Các phương thức tạo được hỗ trợ */
  supportedGenerationMethods?: string[];
  /** Nhiệt độ mặc định */
  defaultTemperature?: number;
  /** Nhiệt độ tối đa */
  maxTemperature?: number;
  /** Top P mặc định */
  defaultTopP?: number;
  /** Top K mặc định */
  defaultTopK?: number;
  /** Có hỗ trợ function calling không */
  supportsFunctionCalling?: boolean;
  /** Có hỗ trợ xử lý hình ảnh không */
  supportsImageInput?: boolean;
  /** Có hỗ trợ xử lý âm thanh không */
  supportsAudioInput?: boolean;
  /** Có hỗ trợ xử lý video không */
  supportsVideoInput?: boolean;
  /** Có hỗ trợ parallel tool calling không */
  supportsParallelToolCalling?: boolean;
  /** Các mức độ reasoning effort được hỗ trợ */
  supportedReasoningEfforts?: string[];
  /** ID của người dùng tạo cấu hình */
  userId?: number;
}
