import { JSONLMessage } from './jsonl-format.interface';

/**
 * Interface cho vai trò trong cuộc hội thoại bán hàng
 */
export enum SalesConversationRole {
  SYSTEM = 'system',
  CUSTOMER = 'user',
  SALES_AGENT = 'assistant'
}

/**
 * Interface cho loại tin nhắn trong cuộc hội thoại bán hàng
 */
export enum SalesMessageType {
  GREETING = 'greeting',
  INQUIRY = 'inquiry',
  PRODUCT_INFO = 'product_info',
  PRICE_NEGOTIATION = 'price_negotiation',
  OBJECTION_HANDLING = 'objection_handling',
  CLOSING = 'closing',
  FOLLOW_UP = 'follow_up',
  OTHER = 'other'
}

/**
 * Interface cho tin nhắn trong cuộc hội thoại bán hàng
 * Mở rộng từ JSONLMessage với các thông tin bổ sung
 */
export interface SalesMessage extends JSONLMessage {
  /** Loại tin nhắn */
  type?: SalesMessageType;
  /** <PERSON><PERSON><PERSON> thực thể được trích xuất từ tin nhắn (sản phẩm, giá, v.v.) */
  entities?: {
    /** Tên sản phẩm */
    product?: string;
    /** Giá sản phẩm */
    price?: number;
    /** Đơn vị tiền tệ */
    currency?: string;
    /** Số lượng */
    quantity?: number;
    /** Thông tin khách hàng */
    customer?: {
      /** Tên khách hàng */
      name?: string;
      /** Số điện thoại */
      phone?: string;
      /** Địa chỉ */
      address?: string;
    };
    /** Các thực thể khác */
    [key: string]: any;
  };
  /** Thời gian gửi tin nhắn */
  timestamp?: Date;
}

/**
 * Interface cho cuộc hội thoại bán hàng
 */
export interface SalesConversation {
  /** ID của cuộc hội thoại */
  id?: number;
  /** Danh sách các tin nhắn trong cuộc hội thoại */
  messages: SalesMessage[];
  /** Thông tin về sản phẩm được đề cập trong cuộc hội thoại */
  products?: {
    /** Tên sản phẩm */
    name: string;
    /** Giá sản phẩm */
    price?: number;
    /** Số lượng */
    quantity?: number;
  }[];
  /** Thông tin về khách hàng */
  customer?: {
    /** Tên khách hàng */
    name?: string;
    /** Số điện thoại */
    phone?: string;
    /** Địa chỉ */
    address?: string;
  };
  /** Trạng thái của cuộc hội thoại (thành công, thất bại, đang xử lý) */
  status?: 'success' | 'failure' | 'in_progress';
  /** Thời gian bắt đầu cuộc hội thoại */
  startTime?: Date;
  /** Thời gian kết thúc cuộc hội thoại */
  endTime?: Date;
  /** Tổng giá trị đơn hàng */
  orderValue?: number;
  /** Ghi chú về cuộc hội thoại */
  notes?: string;
}

/**
 * Interface cho tham số chuyển đổi cuộc hội thoại bán hàng sang định dạng JSONL
 */
export interface ConvertSalesConversationParams {
  /** Danh sách các cuộc hội thoại bán hàng */
  conversations: SalesConversation[];
  /** Có bao gồm thông tin bổ sung không */
  includeMetadata?: boolean;
  /** Có bao gồm tin nhắn hệ thống không */
  includeSystemMessages?: boolean;
  /** Nội dung tin nhắn hệ thống mặc định */
  defaultSystemMessage?: string;
}
