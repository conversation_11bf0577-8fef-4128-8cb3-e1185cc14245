import { Injectable, Logger } from '@nestjs/common';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { MODEL_TRAINING_ERROR_CODES } from '@/modules/model-training/exceptions/model-training.exception';
import { UserFineTuningModelRepository } from '@/modules/model-training/repositories/user-fine-tuning-model.repository';
import { BaseModelRepository } from '@/modules/model-training/repositories/base-model.repository';
import { CreateUserFineTuningModelDto } from '@/modules/model-training/dto/fine-tuning-model/create-user-fine-tuning-model.dto';
import { FineTuningModelQueryDto } from '@/modules/model-training/dto/fine-tuning-model/fine-tuning-model-query.dto';
import { UserFineTuningModelDetailRes, UserFineTuningModelPaginatedRes } from '@/modules/model-training/dto/fine-tuning-model/user-fine-tuning-model.dto';
import { plainToInstance } from 'class-transformer';
import { FineTuningModelIdValidatorHelper } from '@/modules/model-training/helpers/fine-tuning-model-id-validator.helper';
import { UserProviderModelRepository } from '@/modules/model-training/repositories/user-provider-model.repository';
import { TypeProviderEnum, TypeProviderUtil } from '@/modules/model-training/constants/type-provider.enum';
import { BaseModelStatusEnum } from '@/modules/model-training/constants/base-model-status.enum';

/**
 * Service xử lý các thao tác liên quan đến mô hình fine-tuning cho user
 */
@Injectable()
export class FineTuningModelUserService {
  private readonly logger = new Logger(FineTuningModelUserService.name);

  constructor(
    private readonly userFineTuningModelRepository: UserFineTuningModelRepository,
    private readonly baseModelRepository: BaseModelRepository,
    private readonly userProviderModelRepository: UserProviderModelRepository,
    private readonly fineTuningModelIdValidator: FineTuningModelIdValidatorHelper,
  ) {}

  /**
   * Tạo mới mô hình fine-tuning
   * @param dto Dữ liệu tạo mô hình
   * @param userId ID của người dùng
   * @returns Thông tin mô hình đã tạo
   */
  async create(dto: CreateUserFineTuningModelDto, userId: number): Promise<UserFineTuningModelDetailRes> {
    try {
      // Kiểm tra base model có tồn tại không
      const baseModel = await this.baseModelRepository.findById(dto.modelBaseId);
      if (!baseModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND, `Không tìm thấy base model với ID ${dto.modelBaseId}`);
      }

      // Kiểm tra base model có trạng thái APPROVED không
      if (baseModel.status !== BaseModelStatusEnum.APPROVED) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA,
          `Base model với ID ${dto.modelBaseId} không ở trạng thái APPROVED`
        );
      }

      // Kiểm tra provider có tồn tại không và thuộc về user không
      const userProvider = await this.userProviderModelRepository.findByIdAndUserId(dto.providerId, userId);
      if (!userProvider) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA,
          `Provider với ID ${dto.providerId} không tồn tại hoặc không thuộc về người dùng`,
        );
      }

      // Kiểm tra ID fine-tuning model có tồn tại trong provider không
      // Chuyển đổi providerId từ string sang TypeProviderEnum
      const providerType = TypeProviderUtil.getMimeType(baseModel.providerId);
      await this.fineTuningModelIdValidator.validateFineTuningModelId(
        dto.id,
        providerType,
        userProvider.apiKey,
      );

      // Tạo mô hình fine-tuning
      const userFineTuningModel = await this.userFineTuningModelRepository.createUserFineTuningModel({
        id: dto.id,
        name: dto.name || dto.id,
        description: dto.description,
        modelBaseId: dto.modelBaseId,
        providerId: dto.providerId,
        modelId: dto.modelId,
        userId,
      });

      if (!userFineTuningModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_FAILED, 'Tạo mô hình fine-tuning thất bại');
      }

      // Lấy thông tin chi tiết mô hình vừa tạo
      return await this.findById(userFineTuningModel.id, userId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_FAILED, 'Tạo mô hình fine-tuning thất bại');
    }
  }

  /**
   * Lấy danh sách mô hình fine-tuning của người dùng với phân trang
   * @param userId ID của người dùng
   * @param query Tham số truy vấn và phân trang
   * @returns Danh sách mô hình fine-tuning với phân trang
   */
  async findAllByUserId(userId: number, query: FineTuningModelQueryDto): Promise<PaginatedResult<UserFineTuningModelPaginatedRes>> {
    try {
      const result = await this.userFineTuningModelRepository.findAllByUserIdPaginated(userId, query);

      // Chuyển đổi kết quả sang DTO
      const items = result.items.map((item: any) => {
        const dto = plainToInstance(UserFineTuningModelPaginatedRes, {
          id: item.id,
          name: item.name,
          description: item.description,
          token: item.token,
          status: item.status,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          baseModelName: item.baseModelName,
          modelBaseId: item.modelBaseId,
          providerId: item.providerId,
          providerName: '',  // Sẽ lấy từ service khác nếu cần
          providerType: '',  // Sẽ lấy từ service khác nếu cần
          modelId: item.modelId,
        });
        return dto;
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR, 'Lấy danh sách mô hình fine-tuning thất bại');
    }
  }

  /**
   * Lấy thông tin chi tiết mô hình fine-tuning theo ID
   * @param id ID của mô hình fine-tuning
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết mô hình fine-tuning
   */
  async findById(id: string, userId: number): Promise<UserFineTuningModelDetailRes> {
    try {
      const userFineTuningModel = await this.userFineTuningModelRepository.findByIdAndUserId(id, userId);
      if (!userFineTuningModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_NOT_FOUND, `Không tìm thấy mô hình fine-tuning với ID ${id}`);
      }

      // Chuyển đổi kết quả sang DTO
      const modelData = userFineTuningModel as any;
      const dto = plainToInstance(UserFineTuningModelDetailRes, {
        id: modelData.id,
        name: modelData.name,
        description: modelData.description,
        token: modelData.token,
        status: modelData.status,
        createdAt: modelData.createdAt,
        updatedAt: modelData.updatedAt,
        userId: modelData.userId,
        modelBaseId: modelData.modelBaseId,
        baseModelName: modelData.baseModelName,
        providerId: modelData.providerId,
        providerName: '',  // Sẽ lấy từ service khác nếu cần
        providerType: '',  // Sẽ lấy từ service khác nếu cần
        modelId: modelData.modelId,
      });

      return dto;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR, 'Lấy thông tin mô hình fine-tuning thất bại');
    }
  }

  /**
   * Xóa mô hình fine-tuning
   * @param id ID của mô hình fine-tuning
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  async delete(id: string, userId: number): Promise<boolean> {
    try {
      const userFineTuningModel = await this.userFineTuningModelRepository.findByIdAndUserId(id, userId);
      if (!userFineTuningModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_NOT_FOUND, `Không tìm thấy mô hình fine-tuning với ID ${id}`);
      }

      const result = await this.userFineTuningModelRepository.deleteUserFineTuningModel(id, userId);
      if (!result) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED, 'Xóa mô hình fine-tuning thất bại');
      }

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED, 'Xóa mô hình fine-tuning thất bại');
    }
  }

  /**
   * Lấy danh sách tất cả mô hình fine-tuning (cả admin và user) với phân trang
   * @param userId ID của người dùng
   * @param query Tham số truy vấn và phân trang
   * @returns Danh sách mô hình fine-tuning với phân trang
   */
  async findAllPublic(userId: number, query: FineTuningModelQueryDto): Promise<PaginatedResult<any>> {
    try {
      return await this.userFineTuningModelRepository.findAllPublicPaginated(userId, query);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR, 'Lấy danh sách mô hình fine-tuning thất bại');
    }
  }
}
