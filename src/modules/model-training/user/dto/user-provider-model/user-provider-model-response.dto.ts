import { ApiProperty } from '@nestjs/swagger';
import { TypeProviderEnum } from '@modules/model-training/constants/type-provider.enum';

/**
 * DTO cho response của provider model của user
 */
export class UserProviderModelResponseDto {
  /**
   * ID của provider model
   */
  @ApiProperty({
    description: 'ID của provider model',
    example: 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6',
  })
  id: string;

  /**
   * Tên định danh cho provider model
   */
  @ApiProperty({
    description: 'Tên định danh cho provider model',
    example: 'My OpenAI Provider',
  })
  name: string;

  /**
   * Loại nhà cung cấp
   */
  @ApiProperty({
    description: 'Loại nhà cung cấp',
    enum: TypeProviderEnum,
    example: TypeProviderEnum.OPENAI,
  })
  type: TypeProviderEnum;

  /**
   * Thời điểm tạo
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp)',
    example: 1715523521893,
  })
  createdAt: number;
}
