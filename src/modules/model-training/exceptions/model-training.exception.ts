import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho module Model Training (30100-30199)
 */
export const MODEL_TRAINING_ERROR_CODES = {
  SYNC_MODELS_FAILED: new ErrorCode(
    30101,
    'Đồng bộ models từ OpenAI thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  OPENAI_API_KEY_MISSING: new ErrorCode(
    30102,
    'API key của OpenAI không được cấu hình',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  OPENAI_API_ERROR: new ErrorCode(
    30103,
    'Lỗi khi gọi API OpenAI',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  MODEL_NOT_FOUND: new ErrorCode(
    30104,
    'Không tìm thấy',
    HttpStatus.NOT_FOUND,
  ),

  MODEL_ALREADY_EXISTS: new ErrorCode(
    30105,
    'Model đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  INVALID_MODEL_DATA: new ErrorCode(
    30106,
    'Dữ liệu model không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  FINE_TUNING_FAILED: new ErrorCode(
    30107,
    'Fine-tuning model thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  FINE_TUNING_NOT_FOUND: new ErrorCode(
    30108,
    'Không tìm thấy fine-tuning',
    HttpStatus.NOT_FOUND,
  ),

  DATA_FINE_TUNING_FAILED: new ErrorCode(
    30109,
    'Data fine-tuning thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  DATA_FINE_TUNING_NOT_FOUND: new ErrorCode(
    30110,
    'Không tìm thấy data fine-tuning',
    HttpStatus.NOT_FOUND,
  ),
  PROVIDER_NOT_FOUND: new ErrorCode(
    30111,
    'Không tìm thấy nhà cung cấp',
    HttpStatus.NOT_FOUND,
  ),
  NOT_NULL_ARRAY: new ErrorCode(
      30112,
      'Danh sách không được rỗng',
      HttpStatus.BAD_REQUEST
  ),
  INVALID_QUERY_PARAMETERS: new ErrorCode(
    30113,
    'Tham số truy vấn không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  API_ACCESS_ERROR: new ErrorCode(
    30114,
    'Lỗi truy cập API',
    HttpStatus.FORBIDDEN,
  ),

  DATA_FETCH_ERROR: new ErrorCode(
    30115,
    'Lỗi khi lấy dữ liệu',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  UPDATE_FAILED: new ErrorCode(
    30116,
    'Cập nhật thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  DELETE_FAILED: new ErrorCode(
    30117,
    'Xóa thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  INVALID_STATUS: new ErrorCode(
    30118,
    'Trạng thái không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  FORBIDDEN: new ErrorCode(
    30119,
    'Không tìm thấy tài nguyên',
    HttpStatus.NOT_FOUND,
  ),

  BAD_REQUEST: new ErrorCode(
    30120,
    'Yêu cầu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  PROVIDER_NAME_EXISTS: new ErrorCode(
    30121,
    'Tên nhà cung cấp đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  PROVIDER_CREATE_FAILED: new ErrorCode(
    30122,
    'Tạo nhà cung cấp thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  PROVIDER_UPDATE_FAILED: new ErrorCode(
    30123,
    'Cập nhật nhà cung cấp thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  PROVIDER_DELETE_FAILED: new ErrorCode(
    30124,
    'Xóa nhà cung cấp thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  ENCRYPTION_ERROR: new ErrorCode(
    30125,
    'Lỗi mã hóa dữ liệu',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  DECRYPTION_ERROR: new ErrorCode(
    30126,
    'Lỗi giải mã dữ liệu',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  SECRET_KEY_MISSING: new ErrorCode(
    30127,
    'Thiếu khóa bí mật để mã hóa/giải mã',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  USER_PROVIDER_NOT_FOUND: new ErrorCode(
    30128,
    'Không tìm thấy nhà cung cấp của người dùng',
    HttpStatus.NOT_FOUND,
  ),

  INVALID_S3_KEY: new ErrorCode(
    30129,
    'Khóa S3 không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  API_KEY_EXPIRED: new ErrorCode(
    30130,
    'API key đã hết hạn hoặc không hợp lệ',
    HttpStatus.UNAUTHORIZED,
  ),

  MODEL_DELETED: new ErrorCode(
    30131,
    'Model đã bị xóa',
    HttpStatus.GONE,
  ),

  API_KEY_VALIDATION_FAILED: new ErrorCode(
    30132,
    'Kiểm tra API key thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  API_KEY_INVALID: new ErrorCode(
    30133,
    'API key không hợp lệ hoặc không có quyền truy cập',
    HttpStatus.UNAUTHORIZED,
  ),
};
