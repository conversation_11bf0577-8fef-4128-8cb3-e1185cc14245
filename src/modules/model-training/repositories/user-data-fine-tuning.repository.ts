import { QueryDto } from '@/common/dto';
import { PaginatedResult } from '@/common/response';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UserDataFineTuning } from '../entities/user-data-fine-tuning.entity';

/**
 * Interface cho kết quả truy vấn dữ liệu fine-tuning của người dùng
 */
export interface UserDataFineTuningResult {
  /** ID của bản ghi */
  id: string;
  /** ID của người dùng */
  userId: number;
  /** ID của nguồn dữ liệu (nếu là bản sao) */
  sourceId: string | null;
  /** Tên của bộ dữ liệu fine-tuning */
  name: string | null;
  /** Mô tả chi tiết về bộ dữ liệu fine-tuning */
  description: string | null;
  /** Thời điểm tạo bộ dữ liệu (timestamp millis) */
  createdAt: number;
}

/**
 * Interface cho kết quả chi tiết dữ liệu fine-tuning của người dùng
 */
export interface UserDataFineTuningDetailResult extends UserDataFineTuningResult {
  /** Dữ liệu huấn luyện */
  trainData: any;
  /** Dữ liệu kiểm thử */
  validationData: any;
  /** Thời điểm cập nhật bộ dữ liệu gần nhất (timestamp millis) */
  updatedAt: number;
  /** Cờ đánh dấu bộ dữ liệu có được rao bán hay không */
  isForSale: boolean;
}

/**
 * Interface cho kết quả truy vấn dữ liệu fine-tuning của người dùng theo nguồn
 */
export interface UserDataFineTuningSourceResult extends UserDataFineTuningResult {
  /** Thời điểm cập nhật bộ dữ liệu gần nhất (timestamp millis) */
  updatedAt: number;
}

/**
 * Repository cho bảng user_data_fine_tuning
 * Cung cấp các phương thức truy vấn và thao tác với dữ liệu fine-tuning của người dùng
 */
@Injectable()
export class UserDataFineTuningRepository extends Repository<UserDataFineTuning> {
  private readonly logger = new Logger(UserDataFineTuningRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserDataFineTuning, dataSource.createEntityManager());
  }

/**
   * Tìm kiếm dữ liệu fine-tuning của người dùng với phân trang
   * @param userId ID của người dùng
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các bản ghi user_data_fine_tuning với thông tin từ data_fine_tuning
   */
  async findByUserId(userId: number, query: QueryDto): Promise<PaginatedResult<UserDataFineTuningResult>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    // Sử dụng raw query để tránh vấn đề với DISTINCT và ORDER BY
    const queryBuilder = this.dataSource.createQueryBuilder()
      .select([
        'udf.id as id',
        'udf.user_id as "userId"',
        'udf.source_id as "sourceId"',
        'df.name as name',
        'df.description as description',
        'df.created_at as "createdAt"',
      ])
      .from('user_data_fine_tuning', 'udf')
      .leftJoin('data_fine_tuning', 'df', 'df.id = udf.id')
      .where('udf.user_id = :userId', { userId })
      .andWhere('df.deleted_at IS NULL');

    // Tìm kiếm theo tên hoặc mô tả của dữ liệu fine-tuning
    if (query.search) {
      queryBuilder.andWhere(
        '(df.name ILIKE :search OR df.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Đếm tổng số bản ghi
    const countQuery = this.dataSource.createQueryBuilder()
      .select('COUNT(DISTINCT udf.id)', 'count')
      .from('user_data_fine_tuning', 'udf')
      .leftJoin('data_fine_tuning', 'df', 'df.id = udf.id')
      .where('udf.user_id = :userId', { userId })
      .andWhere('df.deleted_at IS NULL');

    if (query.search) {
      countQuery.andWhere(
        '(df.name ILIKE :search OR df.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    const totalItemsResult = await countQuery.getRawOne();
    const totalItems = parseInt(totalItemsResult?.count || '0', 10);

    // Sắp xếp
    queryBuilder.orderBy('df.updated_at', 'DESC');

    // Phân trang
    queryBuilder.offset((page - 1) * limit);
    queryBuilder.limit(limit);

    // Thực hiện truy vấn
    const items = await queryBuilder.getRawMany();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm dữ liệu fine-tuning của người dùng theo ID
   * @param id UUID của bản ghi cần tìm
   * @param userId ID của người dùng (để kiểm tra quyền truy cập)
   * @returns Thông tin chi tiết của bản ghi hoặc null nếu không tìm thấy
   */
  async findByIdAndUserId(id: string, userId: number): Promise<UserDataFineTuningDetailResult | null> {
    try {
      // Sử dụng raw query để tránh vấn đề với DISTINCT và ORDER BY
      const queryBuilder = this.dataSource.createQueryBuilder()
        .select([
          'udf.id as id',
          'udf.user_id as "userId"',
          'udf.source_id as "sourceId"',
          'df.name as name',
          'df.description as description',
          'df.train_data as "trainData"',
          'df.validation_data as "validationData"',
          'df.created_at as "createdAt"',
          'df.updated_at as "updatedAt"',
          'df.is_for_sale as "isForSale"'
        ])
        .from('user_data_fine_tuning', 'udf')
        .leftJoin('data_fine_tuning', 'df', 'df.id = udf.id')
        .where('udf.id = :id', { id })
        .andWhere('udf.user_id = :userId', { userId })
        .andWhere('df.deleted_at IS NULL');

      const result = await queryBuilder.getRawOne();
      return result as UserDataFineTuningDetailResult | null;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm dữ liệu fine-tuning của người dùng với ID ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm dữ liệu fine-tuning của người dùng theo ID nguồn
   * @param sourceId ID của nguồn dữ liệu
   * @param userId ID của người dùng
   * @returns Danh sách các bản ghi user_data_fine_tuning
   */
  async findBySourceIdAndUserId(sourceId: string, userId: number): Promise<UserDataFineTuningResult[]> {
    try {
      // Sử dụng raw query để tránh vấn đề với DISTINCT và ORDER BY
      const queryBuilder = this.dataSource.createQueryBuilder()
        .select([
          'udf.id as id',
          'udf.user_id as "userId"',
          'udf.source_id as "sourceId"',
          'df.name as name',
          'df.description as description',
          'df.created_at as "createdAt"',
          'df.updated_at as "updatedAt"',
        ])
        .from('user_data_fine_tuning', 'udf')
        .leftJoin('data_fine_tuning', 'df', 'df.id = udf.id')
        .where('udf.source_id = :sourceId', { sourceId })
        .andWhere('udf.user_id = :userId', { userId })
        .andWhere('df.deleted_at IS NULL')
        .orderBy('df.updated_at', 'DESC');

      const results = await queryBuilder.getRawMany();
      return results as UserDataFineTuningResult[];
    } catch (error) {
      this.logger.error(`Lỗi khi tìm dữ liệu fine-tuning của người dùng với sourceId ${sourceId}: ${error.message}`, error.stack);
      return [];
    }
  }
}
