import { PaginatedResult } from '@/common/response';
import { User } from '@modules/user/entities/user.entity';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { FineTuningModelQueryDto } from '../dto/fine-tuning-model/fine-tuning-model-query.dto';
import { BaseModel } from '../entities/base-model.entity';
import { FineTuningModel } from '../entities/fine-tuning-model.entity';
import { UserFineTuningModel } from '../entities/user-fine-tuning-model.entity';
import { UserProviderModel } from '../entities/user-provider-model.entity';

/**
 * Repository cho bảng user_fine_tuning_models
 * Quản lý các thao tác CRUD và truy vấn liên quan đến mô hình fine-tuning của người dùng
 */
@Injectable()
export class UserFineTuningModelRepository extends Repository<UserFineTuningModel> {
  private readonly logger = new Logger(UserFineTuningModelRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserFineTuningModel, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản với các join cần thiết
   * @returns SelectQueryBuilder đã được cấu hình với các join
   */
  private createBaseQuery(): SelectQueryBuilder<UserFineTuningModel> {
    return this.createQueryBuilder('userModel')
      .leftJoin(FineTuningModel, 'fineTuningModel', 'fineTuningModel.id = userModel.id')
      .leftJoin(BaseModel, 'baseModel', 'baseModel.id = userModel.model_base_id')
      .leftJoin(UserProviderModel, 'providerModel', 'providerModel.id = userModel.provider_id')
      .leftJoin(User, 'user', 'user.id = userModel.user_id');
  }

  /**
   * Tìm mô hình fine-tuning của người dùng theo ID và userId
   * @param id ID của mô hình fine-tuning
   * @param userId ID của người dùng
   * @returns Thông tin mô hình fine-tuning hoặc null nếu không tìm thấy
   */
  async findByIdAndUserId(id: string, userId: number): Promise<any | null> {
    try {
      const queryBuilder = this.createBaseQuery()
        .select([
          'userModel.id',
          'userModel.userId',
          'userModel.modelBaseId',
          'userModel.providerId',
          'userModel.modelId',
          'fineTuningModel.name',
          'fineTuningModel.description',
          'fineTuningModel.token',
          'fineTuningModel.status',
          'fineTuningModel.detailId',
          'fineTuningModel.createdAt',
          'fineTuningModel.updatedAt',
          'baseModel.name as baseModelName',
          'providerModel.name as providerName',
          'providerModel.type as providerType',
        ])
        .where('userModel.id = :id', { id })
        .andWhere('userModel.user_id = :userId', { userId });

      // Chuyển đổi sang raw query để lấy dữ liệu dạng flat
      const rawQuery = queryBuilder.getQuery();
      const parameters = queryBuilder.getParameters() as any[];
      const result = await this.dataSource.query(rawQuery, parameters);

      return result.length > 0 ? result[0] : null;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm mô hình fine-tuning người dùng với ID ${id} và userId ${userId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm tất cả mô hình fine-tuning của người dùng với phân trang
   * @param userId ID của người dùng
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các mô hình fine-tuning của người dùng
   */
  async findAllByUserIdPaginated(userId: number, query: FineTuningModelQueryDto): Promise<PaginatedResult<any>> {
    try {
      const page = query.page ?? 1;
      const limit = query.limit ?? 10;
      const skip = (page - 1) * limit;

      const queryBuilder = this.createBaseQuery()
        .select([
          'userModel.id',
          'userModel.userId',
          'userModel.modelBaseId',
          'userModel.providerId',
          'userModel.modelId',
          'fineTuningModel.name',
          'fineTuningModel.description',
          'fineTuningModel.token',
          'fineTuningModel.status',
          'fineTuningModel.createdAt',
          'fineTuningModel.updatedAt',
          'baseModel.name as baseModelName',
          'providerModel.name as providerName',
          'providerModel.type as providerType',
        ])
        .where('userModel.user_id = :userId', { userId });

      // Tìm kiếm theo tên hoặc mô tả
      if (query.search) {
        queryBuilder.andWhere(
          '(fineTuningModel.name ILIKE :search OR fineTuningModel.description ILIKE :search)',
          { search: `%${query.search}%` },
        );
      }

      // Lọc theo nhà cung cấp
      if (query.providerId) {
        queryBuilder.andWhere('userModel.provider_id = :providerId', { providerId: query.providerId });
      }

      // Sắp xếp
      queryBuilder.orderBy(`fineTuningModel.${query.sortBy || 'createdAt'}`, query.sortDirection || 'DESC');

      // Đếm tổng số bản ghi
      const countQuery = `SELECT COUNT(*) FROM (${queryBuilder.getQuery()}) as count_query`;
      const countParameters = queryBuilder.getParameters() as any[];
      const countResult = await this.dataSource.query(countQuery, countParameters);
      const totalItems = parseInt(countResult[0].count, 10);

      // Phân trang
      queryBuilder.limit(limit).offset(skip);

      // Thực hiện truy vấn
      const rawQuery = queryBuilder.getQuery();
      const parameters = queryBuilder.getParameters() as any[];
      const items = await this.dataSource.query(rawQuery, parameters);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning người dùng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm mô hình fine-tuning của người dùng theo ID
   * @param id ID của mô hình fine-tuning
   * @returns Thông tin mô hình fine-tuning hoặc null nếu không tìm thấy
   */
  async findById(id: string): Promise<UserFineTuningModel | null> {
    try {
      return await this.findOne({ where: { id } });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm mô hình fine-tuning người dùng với ID ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tạo mới mô hình fine-tuning của người dùng
   * @param data Dữ liệu tạo mô hình
   * @returns Mô hình đã tạo hoặc null nếu tạo thất bại
   */
  async createUserFineTuningModel(data: {
    id: string;
    name: string;
    description?: string;
    modelBaseId: string;
    providerId: string;
    modelId?: string;
    userId: number;
  }): Promise<UserFineTuningModel | null> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Tạo bản ghi trong bảng fine_tuning_models
      const fineTuningModel = new FineTuningModel();
      fineTuningModel.id = data.id;
      fineTuningModel.name = data.name || data.id;
      fineTuningModel.description = data.description || '';
      fineTuningModel.token = 0;
      fineTuningModel.status = 'completed';
      fineTuningModel.isManual = false;
      fineTuningModel.detailId = '';

      await queryRunner.manager.save(fineTuningModel);

      // Tạo bản ghi trong bảng user_fine_tuning_models
      const userFineTuningModel = new UserFineTuningModel();
      userFineTuningModel.id = data.id;
      userFineTuningModel.userId = data.userId;
      userFineTuningModel.modelBaseId = data.modelBaseId;
      userFineTuningModel.providerId = data.providerId;
      userFineTuningModel.modelId = data.modelId || '';

      await queryRunner.manager.save(userFineTuningModel);

      await queryRunner.commitTransaction();
      return userFineTuningModel;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi khi tạo mô hình fine-tuning người dùng: ${error.message}`, error.stack);
      return null;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Xóa mô hình fine-tuning của người dùng
   * @param id ID của mô hình fine-tuning
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công, false nếu xóa thất bại
   */
  async deleteUserFineTuningModel(id: string, userId: number): Promise<boolean> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Kiểm tra xem mô hình có thuộc về người dùng không
      const userFineTuningModel = await this.findByIdAndUserId(id, userId);
      if (!userFineTuningModel) {
        return false;
      }

      // Cập nhật bản ghi trong bảng fine_tuning_models
      const now = Date.now();
      await queryRunner.manager.update(
        FineTuningModel,
        { id },
        { deletedAt: now },
      );

      await queryRunner.commitTransaction();
      return true;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi khi xóa mô hình fine-tuning người dùng: ${error.message}`, error.stack);
      return false;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Lấy danh sách tất cả mô hình fine-tuning (cả admin và user) với phân trang
   * @param userId ID của người dùng
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các mô hình fine-tuning
   */
  async findAllPublicPaginated(userId: number, query: FineTuningModelQueryDto): Promise<PaginatedResult<any>> {
    try {
      const page = query.page ?? 1;
      const limit = query.limit ?? 10;
      const skip = (page - 1) * limit;

      // Truy vấn mô hình của admin
      const adminQueryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          'ftm.id',
          'ftm.name',
          'ftm.description',
          'ftm.token',
          'ftm.status',
          'ftm.created_at as "createdAt"',
          'ftm.updated_at as "updatedAt"',
          'bm.id as "modelBaseId"',
          'bm.name as "baseModelName"',
          'bm.provider_id as "providerId"',
          '"admin" as "ownerType"',
        ])
        .from('fine_tuning_models', 'ftm')
        .innerJoin('admin_fine_tuning_models', 'afm', 'afm.id = ftm.id')
        .leftJoin('base_models', 'bm', 'bm.id = afm.model_base_id')
        .where('ftm.deleted_at IS NULL')
        .andWhere('afm.is_private = false');

      // Truy vấn mô hình của user hiện tại
      const userQueryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          'ftm.id',
          'ftm.name',
          'ftm.description',
          'ftm.token',
          'ftm.status',
          'ftm.created_at as "createdAt"',
          'ftm.updated_at as "updatedAt"',
          'bm.id as "modelBaseId"',
          'bm.name as "baseModelName"',
          'ufm.provider_id as "providerId"',
          'ufm.model_id as "modelId"',
          '"user" as "ownerType"',
        ])
        .from('fine_tuning_models', 'ftm')
        .innerJoin('user_fine_tuning_models', 'ufm', 'ufm.id = ftm.id')
        .leftJoin('base_models', 'bm', 'bm.id = ufm.model_base_id')
        .where('ftm.deleted_at IS NULL')
        .andWhere('ufm.user_id = :userId', { userId });

      // Kết hợp hai truy vấn
      const combinedQueryBuilder = this.dataSource
        .createQueryBuilder()
        .select('*')
        .from(`(${adminQueryBuilder.getQuery()} UNION ALL ${userQueryBuilder.getQuery()})`, 'combined');

      // Áp dụng các điều kiện lọc
      if (query.search) {
        combinedQueryBuilder.andWhere(
          '(combined.name ILIKE :search OR combined.description ILIKE :search)',
          { search: `%${query.search}%` },
        );
      }

      if (query.providerId) {
        combinedQueryBuilder.andWhere('combined."providerId" = :providerId', { providerId: query.providerId });
      }

      // Sắp xếp
      combinedQueryBuilder.orderBy(`combined."${query.sortBy || 'createdAt'}"`, query.sortDirection || 'DESC');

      // Đếm tổng số bản ghi
      const countQuery = `SELECT COUNT(*) FROM (${combinedQueryBuilder.getQuery()}) as count_query`;
      const parameters = combinedQueryBuilder.getParameters() as any[];
      const countResult = await this.dataSource.query(countQuery, parameters);
      const totalItems = parseInt(countResult[0].count, 10);

      // Phân trang
      combinedQueryBuilder.limit(limit).offset(skip);

      // Thực hiện truy vấn
      const queryParameters = combinedQueryBuilder.getParameters() as any[];
      const items = await this.dataSource.query(
        combinedQueryBuilder.getQuery(),
        queryParameters,
      );

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning: ${error.message}`, error.stack);
      throw error;
    }
  }
}
