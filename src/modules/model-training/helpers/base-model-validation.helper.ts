import { AdminProviderModel, BaseModel } from '@modules/model-training/entities';
import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { MODEL_TRAINING_ERROR_CODES } from '../exceptions';
import { BaseModelStatusEnum } from '../constants/base-model-status.enum';

@Injectable()
export class BaseModelValidationHelper {
  /**
   * Validates that a provider exists
   * @param provider The provider to validate
   * @throws AppException if the provider does not exist
   */
  validateProviderExists(provider: AdminProviderModel | null): void {
    if (!provider) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA,
        'Provider OPENAI not found',
      );
    }
  }

  /**
   * Validates that a base model exists
   * @param model The base model to validate
   * @throws AppException if the model does not exist
   */
  validateModelExists<T>(model: T | null, name = 'Entity'): T {
  if (!model) {
    throw new AppException(
      MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
      `${name} not found`,
    );
  }
  return model;
}

validateBaseModelStatus(status:BaseModelStatusEnum): void {
    if (status !== BaseModelStatusEnum.APPROVED) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.BAD_REQUEST,
        'Invalid moderation status',
      );
    }
  }

}