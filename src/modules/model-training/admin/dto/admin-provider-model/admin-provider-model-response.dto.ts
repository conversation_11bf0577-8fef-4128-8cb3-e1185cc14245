import { ApiProperty } from '@nestjs/swagger';
import { TypeProviderEnum } from '@modules/model-training/constants/type-provider.enum';

/**
 * DTO cơ bản cho response của provider model
 */
export class AdminProviderModelBaseResponseDto {
  /**
   * ID của provider model (UUID)
   */
  @ApiProperty({
    description: 'ID của provider model (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  /**
   * Tên nhà cung cấp
   */
  @ApiProperty({
    description: 'Tên nhà cung cấp',
    example: 'OpenAI Provider',
  })
  name: string;

  /**
   * Loại nhà cung cấp
   */
  @ApiProperty({
    description: 'Loại nhà cung cấp',
    enum: TypeProviderEnum,
    example: TypeProviderEnum.OPENAI,
  })
  type: TypeProviderEnum;

  /**
   * Thời điểm tạo
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp)',
    example: 1715523521893,
  })
  createdAt: number;
}

/**
 * DTO chi tiết cho response của provider model
 */
export class AdminProviderModelDetailResponseDto extends AdminProviderModelBaseResponseDto {
  /**
   * Thông tin người tạo
   */
  @ApiProperty({
    description: 'Thông tin người tạo',
    example: {
      id: 1,
      name: 'Admin User',
      avatar: 'avatar-url.jpg',
    },
  })
  createdBy: {
    id: number;
    name: string;
    avatar: string | null;
  };

  /**
   * Thời điểm cập nhật
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật (timestamp)',
    example: 1715523521893,
  })
  updatedAt: number;

  /**
   * Thông tin người cập nhật
   */
  @ApiProperty({
    description: 'Thông tin người cập nhật',
    example: {
      id: 1,
      name: 'Admin User',
      avatar: 'avatar-url.jpg',
    },
    nullable: true,
  })
  updatedBy?: {
    id: number;
    name: string;
    avatar: string | null;
  };
}
