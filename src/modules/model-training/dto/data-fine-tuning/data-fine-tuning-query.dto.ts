import { QueryDto} from "@common/dto"; // Giả sử QueryDto đã có sẵn
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import {Type} from "class-transformer";
import { DataFineTunningStatusEnum } from '../../constants/data-fine-tunning.enum';

/**
 * Lớp mở rộng QueryDto để bao gồm trường `status` cho việc lọc DataFineTuning theo trạng thái
 */
export class DataFineTuningQueryDto extends QueryDto {
    @ApiProperty({
        description: 'Trạng thái của DataFineTuning (chỉ cho phép DRAFT hoặc APPROVED)',
        enum: [DataFineTunningStatusEnum.DRAFT, DataFineTunningStatusEnum.APPROVED],
        required: false,
    })
    @IsOptional()
    @IsEnum([DataFineTunningStatusEnum.DRAFT, DataFineTunningStatusEnum.APPROVED], {
        message: 'status chỉ có thể là DRAFT hoặc APPROVED'
    })
    @Type(() => String)
    status?: DataFineTunningStatusEnum;

    @ApiProperty({
        description: 'Trạng thái rao bán của DataFineTuning',
        required: false,
    })
    @Type(() => Boolean)
    @IsBoolean()
    @IsOptional()
    isForSale: boolean;
}
