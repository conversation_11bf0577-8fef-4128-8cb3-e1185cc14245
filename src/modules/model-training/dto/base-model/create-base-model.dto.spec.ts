import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateBaseModelDto } from './base-model.dto';
import { BaseModelStatusEnum } from '@/modules/model-training/constants/base-model-status.enum';

describe('CreateBaseModelDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(CreateBaseModelDto, {
      description: 'Mô hình GPT-4 cho xử lý văn bản',
      providerId: '123e4567-e89b-12d3-a456-************',
      baseInputRate: 5,
      baseOutputRate: 8,
      baseTrainRate: 10,
      fineTuningInputRate: 7,
      fineTuningOutputRate: 9,
      fineTuningTrainRate: 15,
      tokenCount: 50000,
      status: BaseModelStatusEnum.DRAFT
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên báo lỗi khi providerId không phải UUID', async () => {
    // Arrange
    const dto = plainToInstance(CreateBaseModelDto, {
      providerId: 'invalid-uuid',
      baseInputRate: 5,
      baseOutputRate: 8,
      baseTrainRate: 10,
      fineTuningInputRate: 7,
      fineTuningOutputRate: 9,
      fineTuningTrainRate: 15,
      tokenCount: 50000,
      status: BaseModelStatusEnum.DRAFT
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('providerId');
    expect(errors[0].constraints?.isUuid).toContain('Provider ID phải là UUID hợp lệ');
  });

  it('nên báo lỗi khi các rate nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(CreateBaseModelDto, {
      providerId: '123e4567-e89b-12d3-a456-************',
      baseInputRate: 0,
      baseOutputRate: 8,
      baseTrainRate: 10,
      fineTuningInputRate: 7,
      fineTuningOutputRate: 9,
      fineTuningTrainRate: 15,
      tokenCount: 50000,
      status: BaseModelStatusEnum.DRAFT
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const baseInputRateError = errors.find(e => e.property === 'baseInputRate');
    expect(baseInputRateError).toBeDefined();
    expect(baseInputRateError?.constraints?.min).toContain('Base input rate phải lớn hơn 0');
  });

  it('nên báo lỗi khi thiếu các trường bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreateBaseModelDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    
    const requiredFields = ['providerId', 'baseInputRate', 'baseOutputRate', 'baseTrainRate', 
                           'fineTuningInputRate', 'fineTuningOutputRate', 'fineTuningTrainRate', 
                           'tokenCount', 'status'];
    
    requiredFields.forEach(field => {
      const fieldError = errors.find(e => e.property === field);
      expect(fieldError).toBeDefined();
    });
  });
});
