import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho danh sách mô hình fine-tuning với phân trang
 */
@Exclude()
export class FineTuningModelPaginatedRes {
  /**
   * ID định danh duy nhất cho mô hình fine-tuning
   */
  @Expose()
  @ApiProperty({
    description: 'ID định danh duy nhất cho mô hình fine-tuning',
    example: 'ft:gpt-3.5-turbo:openai:custom-model:7p89qrs',
  })
  id: string;

  /**
   * Tên hiển thị của mô hình fine-tuning
   */
  @Expose()
  @ApiProperty({
    description: 'Tên hiển thị của mô hình fine-tuning',
    example: 'Mô hình hỗ trợ khách hàng',
  })
  name: string;

  /**
   * <PERSON>ô tả chi tiết về mô hình và mục đích sử dụng
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về mô hình và mục đích sử dụng',
    example: 'Mô hình được huấn luyện để trả lời các câu hỏi về sản phẩm và dịch vụ',
  })
  description: string;

  /**
   * Số lượng token đã sử dụng trong quá trình fine-tuning
   */
  @Expose()
  @ApiProperty({
    description: 'Số lượng token đã sử dụng trong quá trình fine-tuning',
    example: 10000,
  })
  token: number;

  /**
   * Trạng thái hiện tại của mô hình
   */
  @Expose()
  @ApiProperty({
    description: 'Trạng thái hiện tại của mô hình',
    example: 'completed',
  })
  status: string;

  /**
   * Thời điểm tạo mô hình (timestamp millis)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm tạo mô hình (timestamp millis)',
    example: 1625097600000,
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật mô hình gần nhất (timestamp millis)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm cập nhật mô hình gần nhất (timestamp millis)',
    example: 1625097600000,
  })
  updatedAt: number;

  /**
   * Tên của mô hình gốc
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Tên của mô hình gốc',
    example: 'GPT-3.5 Turbo',
  })
  baseModelName?: string;

  /**
   * Cờ đánh dấu mô hình có ở chế độ riêng tư hay không
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Cờ đánh dấu mô hình có ở chế độ riêng tư hay không',
    example: false,
  })
  isPrivate?: boolean;
}

/**
 * DTO cho chi tiết mô hình fine-tuning
 */
@Exclude()
export class AdminFineTuningModelDetailRes extends FineTuningModelPaginatedRes {
  /**
   * ID của nhân viên tạo mô hình
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'ID của nhân viên tạo mô hình',
    example: 1,
  })
  createdBy?: number;

  /**
   * ID của nhân viên cập nhật mô hình
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'ID của nhân viên cập nhật mô hình',
    example: 2,
  })
  updatedBy?: number;

  /**
   * ID của nhân viên đã xóa mô hình
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'ID của nhân viên đã xóa mô hình',
    example: 3,
  })
  deletedBy?: number;

  /**
   * ID của mô hình gốc được sử dụng để fine-tune
   */
  @Expose()
  @ApiProperty({
    description: 'ID của mô hình gốc được sử dụng để fine-tune',
    example: 'gpt-3.5-turbo',
  })
  modelBaseId: string;

  /**
   * Thông tin chi tiết về người tạo mô hình
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Thông tin chi tiết về người tạo mô hình',
    example: {
      id: 1,
      fullName: 'Nguyễn Văn A',
      avatar: 'https://example.com/avatar.png',
    },
  })
  createdByInfo?: {
    id: number;
    fullName: string;
    avatar?: string;
  };

  /**
   * Thông tin chi tiết về người cập nhật mô hình
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Thông tin chi tiết về người cập nhật mô hình',
    example: {
      id: 2,
      fullName: 'Trần Thị B',
      avatar: 'https://example.com/avatar2.png',
    },
  })
  updatedByInfo?: {
    id: number;
    fullName: string;
    avatar?: string;
  };
}
