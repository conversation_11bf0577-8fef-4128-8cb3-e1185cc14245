import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_fine_tuning_models trong cơ sở dữ liệu
 * Lưu thông tin các mô hình fine-tune do người dùng tạo, li<PERSON>n kết với mô hình gốc và nhà cung cấp
 */
@Entity('user_fine_tuning_models')
export class UserFineTuningModel {
  /**
   * ID mô hình fine-tune, tham chiếu đến bảng fine_tuning_models
   */
  @PrimaryColumn({ name: 'id', length: 100 })
  id: string;

  /**
   * ID của người dùng sở hữu mô hình (tham chiếu bảng users)
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * ID của mô hình gốc được dùng để fine-tune (tham chiếu bảng base_models)
   */
  @Column('uuid', { name: 'model_base_id' }) 
  modelBaseId: string;

  /**
   * ID của nhà cung cấp hoặc phiên bản mô hình cụ thể của người dùng (tham chiếu bảng user_provider_models)
   */
  @Column({ name: 'provider_id', type: 'uuid' })
  providerId: string;

  /**
   * Tên hoặc mã định danh riêng của mô hình fine-tune do người dùng định nghĩa
   */
  @Column({ name: 'model_id', length: 100 })
  modelId: string;
}
