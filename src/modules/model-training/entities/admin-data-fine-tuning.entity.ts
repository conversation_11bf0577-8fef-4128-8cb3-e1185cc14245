import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToOne, PrimaryColumn } from 'typeorm';
import { DataFineTuning } from './data-fine-tuning.entity';
import { Employee } from '@modules/employee/entities/employee.entity';

/**
 * Entity đại diện cho bảng admin_data_fine_tuning trong cơ sở dữ liệu
 * Quản lý thông tin các mô hình fine-tune ở cấp độ quản trị, gồm người tạo, cập nhật, xóa và mô hình liên quan
 */
@Entity('admin_data_fine_tuning')
export class AdminDataFineTuning {
  /**
   * UUID định danh duy nhất, tham chiếu đến bảng data_fine_tuning
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * ID của nhân viên tạo bản ghi mô hình (tham chiếu bảng employees)
   */
  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật bản ghi mô hình (tham chiếu bảng employees)
   */
  @Column({ name: 'updated_by', type: 'int', nullable: true })
  updatedBy: number | null;

  /**
   * ID của nhân viên xóa bản ghi mô hình (tham chiếu bảng employees)
   */
  @Column({ name: 'deleted_by', type: 'int', nullable: true })
  deletedBy: number | null;

  /**
   * Quan hệ với bảng data_fine_tuning
   */
  @OneToOne(() => DataFineTuning)
  @JoinColumn({ name: 'id' })
  dataFineTuning: DataFineTuning;

  /**
   * Quan hệ với bảng employees (người tạo)
   */
  @ManyToOne(() => Employee)
  @JoinColumn({ name: 'created_by' })
  createdByEmployee: Employee;

  /**
   * Quan hệ với bảng employees (người cập nhật)
   */
  @ManyToOne(() => Employee)
  @JoinColumn({ name: 'updated_by' })
  updatedByEmployee: Employee;

  /**
   * Quan hệ với bảng employees (người xóa)
   */
  @ManyToOne(() => Employee)
  @JoinColumn({ name: 'deleted_by' })
  deletedByEmployee: Employee;
}
