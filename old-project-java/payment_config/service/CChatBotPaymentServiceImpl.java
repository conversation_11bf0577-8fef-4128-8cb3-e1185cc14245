package com.redon_agency.chatbot.user.payment_config.service;

import com.redon_agency.chatbot.entity.ChatBot;
import com.redon_agency.chatbot.entity.ChatBotPaymentGateways;
import com.redon_agency.chatbot.entity.ElectronicPaymentGateway;
import com.redon_agency.chatbot.exception.AppException;
import com.redon_agency.chatbot.exception.ErrorCode;
import com.redon_agency.chatbot.repository.ChatBotPaymentGatewaysRepository;
import com.redon_agency.chatbot.repository.ChatBotRepository;
import com.redon_agency.chatbot.repository.ElectronicPaymentGatewayRepository;
import com.redon_agency.chatbot.user.payment_config.dto.response.CChatBotPaymentRes;
import com.redon_agency.chatbot.user.payment_config.dto.CChatBotTypePayment;
import com.redon_agency.chatbot.user.payment_config.mapper.ChatBotPaymentGetwayMapper;
import com.redon_agency.chatbot.utils.enum_utils.PaymentChatbotEnum;
import jakarta.persistence.Tuple;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CChatBotPaymentServiceImpl implements CChatBotPaymentService {

    ChatBotRepository chatBotRepository;
    ChatBotPaymentGetwayMapper chatBotPaymentGetwayMapper;
    private final ChatBotPaymentGatewaysRepository chatBotPaymentGatewaysRepository;
    private final ElectronicPaymentGatewayRepository electronicPaymentGatewayRepository;

    @Override
    public List<CChatBotPaymentRes> listChatBotPayment() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Integer userId = Integer.valueOf(authentication.getName());

        List<Tuple> tuples = chatBotRepository.listChatBotPaymentByUserId(userId);
        return chatBotPaymentGetwayMapper.toListCChatBotPaymentRes(tuples);
    }

    @Override
    public void settingTypePayment(Integer chatBotId, PaymentChatbotEnum paymentMethod) {
        ChatBot chatBot = chatBotRepository.findById(chatBotId).orElseThrow(
                () -> new AppException(ErrorCode.CHAT_BOT_NOT_EXIST)
        );

        chatBot.setPaymentMethod(paymentMethod);
        chatBotRepository.save(chatBot);
    }

    @Override
    public CChatBotTypePayment viewChatBotTypePayment(Integer chatBotId) {
        ChatBot chatBot = chatBotRepository.findById(chatBotId).orElseThrow(
                () -> new AppException(ErrorCode.CHAT_BOT_NOT_EXIST)
        );

        return CChatBotTypePayment.builder()
                .type(chatBot.getPaymentMethod())
                .build();
    }

    @Override
    public void chatBotOnlineBankingSettingPayment(
            Integer chatBotId,
            Integer id
    ) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        int userId = Integer.parseInt(authentication.getName());

        ChatBotPaymentGateways chatBotPaymentGateways = chatBotPaymentGatewaysRepository.findByChatBotId(chatBotId).orElse(
                ChatBotPaymentGateways.builder()
                        .chatBotId(chatBotId)
                        .createdAt(LocalDateTime.now())
                        .build()
        );

        ElectronicPaymentGateway gateway = electronicPaymentGatewayRepository.findByIdAndUserId(id, userId).orElseThrow(
                () -> new AppException(ErrorCode.ELECTRONIC_PAYMENT_NOT_FOUND)
        );

        PaymentChatbotEnum type =  PaymentChatbotEnum.ONLINE_BANKING;
        chatBotRepository.setPaymentType(type.name(), chatBotId);

        chatBotPaymentGateways.setElectronicPaymentGetwayId(gateway.getId());
        chatBotPaymentGateways.setUpdatedAt(LocalDateTime.now());

        chatBotPaymentGatewaysRepository.save(chatBotPaymentGateways);
    }
}
