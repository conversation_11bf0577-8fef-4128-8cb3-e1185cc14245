package com.redon_agency.chatbot.user.payment_config.dto.response;

import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CAccountRes implements Serializable {
    private Integer id;
    private String accountId;
    private String iconBank;
    private String bankCode;
    private String accountNumber;
    private String accountName;
    private String status;

    private Boolean isVA;
    private Boolean isDelete;
}
