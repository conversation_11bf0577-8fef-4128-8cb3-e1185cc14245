package com.redon_agency.chatbot.user.payment_config.dto.response;

import com.redon_agency.chatbot.utils.enum_utils.PaymentChatbotEnum;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CChatBotPaymentRes {
    // Type
    PaymentChatbotEnum type;

    // Bank
    Integer id;
    String bankCode;
    String accountNumber;
    String accountName;
    String iconPath;
    Boolean isVA;

    // Chatbot
    Integer chatBotId;
    String chatBotName;
    String chatBotIcon;
    String position;

    // Status
    Boolean isLinked;
}
